<?php

// Simple standalone WhatsApp webhook
// This bypasses <PERSON><PERSON> entirely to avoid any framework issues

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log function
function logMessage($message, $data = []) {
    $logEntry = date('Y-m-d H:i:s') . " - " . $message;
    if (!empty($data)) {
        $logEntry .= " - " . json_encode($data);
    }
    $logEntry .= "\n";

    file_put_contents(__DIR__ . '/../storage/logs/whatsapp-webhook.log', $logEntry, FILE_APPEND | LOCK_EX);
}

// WhatsApp typing indicator function
function sendWhatsAppTypingIndicator($phoneNumber, $action = 'typing_on') {
    // WhatsApp API credentials
    $accessToken = 'EAAMse8zYnIgBOZB5ndUzNyULm0TXTNoLbTII7rbxwvf294wVjlhexnToXMaRvBJYaNzBY5suAqExEIrqQXGBHG5E2WzCBg2s8ApmFpbZAfPZADRff02nV7UfjI2UQwtjBpdJIG0sISRuYMthZClDkoWIgFz6yEpNwvxRU5ZAKIZAab1Uh9HG2XdC3hMlqTLNtVFwZDZD';
    $phoneNumberId = '709649418889125';

    $url = "https://graph.facebook.com/v18.0/{$phoneNumberId}/messages";

    // Use WhatsApp's proper typing indicator format
    $payload = [
        'messaging_product' => 'whatsapp',
        'recipient_type' => 'individual',
        'to' => $phoneNumber,
        'type' => 'text',
        'text' => [
            'body' => '💭 Ije is thinking...'
        ]
    ];

    $headers = [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    $success = ($httpCode === 200 && !$error);

    logMessage("WhatsApp typing indicator sent", [
        'to' => $phoneNumber,
        'action' => $action,
        'success' => $success,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ]);

    return $success;
}

// Send a temporary "thinking" message that gets deleted/replaced
function sendWhatsAppThinkingMessage($phoneNumber, $messageType = 'text') {
    $thinkingMessages = [
        'text' => [
            '🤔 Ije is thinking...',
            '💭 Processing your question...',
            '🧠 Analyzing your request...',
            '⏳ Just a moment...',
            '🔍 Looking into this...'
        ],
        'image' => [
            '📸 Analyzing your plant image...',
            '🔍 Examining the photo...',
            '🌱 Identifying plant health...',
            '🧬 Running AI analysis...',
            '📊 Processing image data...'
        ]
    ];

    $messages = $thinkingMessages[$messageType] ?? $thinkingMessages['text'];
    $randomMessage = $messages[array_rand($messages)];

    return sendWhatsAppMessage($phoneNumber, $randomMessage);
}

// Mark message as read
function markWhatsAppMessageAsRead($phoneNumber, $messageId) {
    $accessToken = 'EAAMse8zYnIgBOZB5ndUzNyULm0TXTNoLbTII7rbxwvf294wVjlhexnToXMaRvBJYaNzBY5suAqExEIrqQXGBHG5E2WzCBg2s8ApmFpbZAfPZADRff02nV7UfjI2UQwtjBpdJIG0sISRuYMthZClDkoWIgFz6yEpNwvxRU5ZAKIZAab1Uh9HG2XdC3hMlqTLNtVFwZDZD';
    $phoneNumberId = '709649418889125';

    $url = "https://graph.facebook.com/v18.0/{$phoneNumberId}/messages";

    $payload = [
        'messaging_product' => 'whatsapp',
        'status' => 'read',
        'message_id' => $messageId
    ];

    $headers = [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return ($httpCode === 200);
}

// WhatsApp sending function
function sendWhatsAppMessage($phoneNumber, $message) {
    // WhatsApp API credentials (you'll need to set these)
    $accessToken = 'EAAMse8zYnIgBOZB5ndUzNyULm0TXTNoLbTII7rbxwvf294wVjlhexnToXMaRvBJYaNzBY5suAqExEIrqQXGBHG5E2WzCBg2s8ApmFpbZAfPZADRff02nV7UfjI2UQwtjBpdJIG0sISRuYMthZClDkoWIgFz6yEpNwvxRU5ZAKIZAab1Uh9HG2XdC3hMlqTLNtVFwZDZD';
    $phoneNumberId = '709649418889125';

    $url = "https://graph.facebook.com/v18.0/{$phoneNumberId}/messages";

    $payload = [
        'messaging_product' => 'whatsapp',
        'to' => $phoneNumber,
        'type' => 'text',
        'text' => ['body' => $message]
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ]);
    // Ultra-fast timeout for instant responses
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    curl_setopt($ch, CURLOPT_TCP_NODELAY, 1);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    logMessage("WhatsApp API call", [
        'to' => $phoneNumber,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ]);

    return $httpCode === 200 && !$error;
}

// Process message through Ije AI chatbot
function processWithIjeAI($phoneNumber, $messageText, $userName) {
    try {
        // Load Laravel to access the chatbot service
        require_once __DIR__ . '/../vendor/autoload.php';
        $app = require_once __DIR__ . '/../bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

        // Check if this is a weather query and handle it with real weather data
        $weatherLocation = \App\Services\WhatsAppWeatherService::extractLocationFromMessage($messageText);
        if ($weatherLocation) {
            logMessage("Weather query detected", [
                'phone' => $phoneNumber,
                'location' => $weatherLocation,
                'message' => $messageText
            ]);

            $weatherService = new \App\Services\WhatsAppWeatherService();
            $weatherResult = $weatherService->getWeatherForLocation($weatherLocation);

            if ($weatherResult['success']) {
                logMessage("Weather data retrieved successfully", [
                    'phone' => $phoneNumber,
                    'location' => $weatherLocation
                ]);
                return $weatherResult['formatted_message'];
            } else {
                logMessage("Weather data retrieval failed", [
                    'phone' => $phoneNumber,
                    'location' => $weatherLocation,
                    'error' => $weatherResult['error']
                ]);
                // Fall through to AI processing with error context
                $messageText .= " [Weather service unavailable: " . $weatherResult['message'] . "]";
            }
        }

        logMessage("Processing with Ije AI", [
            'phone' => $phoneNumber,
            'message' => $messageText,
            'user_name' => $userName
        ]);

        // Find or create WhatsApp user
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phoneNumber);

        $user = \App\Models\User::where('phone_number', $cleanPhone)
            ->orWhere('phone_number', $phoneNumber)
            ->first();

        if (!$user) {
            // Create new WhatsApp guest user
            $user = \App\Models\User::create([
                'name' => $userName,
                'email' => 'whatsapp_' . $cleanPhone . '@guest.riwe.io',
                'phone_number' => $cleanPhone,
                'password' => bcrypt(\Illuminate\Support\Str::random(32)),
                'is_whatsapp_guest' => true,
                'email_verified_at' => now(),
                'preferred_currency' => 'NGN',
                'location' => 'Nigeria',
            ]);

            // Assign default role
            $defaultRole = \App\Models\Role::where('name', 'user')->first();
            if ($defaultRole) {
                $user->roles()->attach($defaultRole->id);
            }

            logMessage("Created new WhatsApp user", [
                'user_id' => $user->id,
                'phone' => $cleanPhone,
                'name' => $userName
            ]);

            // Send welcome message for new users
            $welcomeMessage = "🌾 Welcome to Ije AI, {$userName}! 🌾\n\n";
            $welcomeMessage .= "I'm Ije, your AI farming assistant from Riwe. I'm here to help you with:\n\n";
            $welcomeMessage .= "🌱 Crop management advice\n";
            $welcomeMessage .= "🌤️ Weather forecasts for your farm\n";
            $welcomeMessage .= "🛡️ Insurance guidance\n";
            $welcomeMessage .= "💰 Financial planning\n";
            $welcomeMessage .= "📊 Market insights\n\n";
            $welcomeMessage .= "💡 *Want full access to Riwe features?*\n";
            $welcomeMessage .= "Type 'SIGNUP' to create your free Riwe account and unlock:\n";
            $welcomeMessage .= "• Farm management tools\n";
            $welcomeMessage .= "• Insurance services\n";
            $welcomeMessage .= "• Financial services\n";
            $welcomeMessage .= "• Market insights\n\n";
            $welcomeMessage .= "Now, let me help you with your question: \"{$messageText}\"";

            // Process the actual message through chatbot
            $chatbotService = app(\App\Services\ChatbotService::class);
            $response = $chatbotService->processMessage($user, $messageText);

            if ($response['success'] && isset($response['bot_message']['message'])) {
                return $welcomeMessage . "\n\n" . $response['bot_message']['message'];
            } else {
                return $welcomeMessage;
            }
        } else {
            // Existing user - check for signup commands first
            $messageUpper = strtoupper(trim($messageText));

            if ($messageUpper === 'SIGNUP' && $user->is_whatsapp_guest) {
                return handleSignupCommand($user, $phoneNumber);
            } elseif (strpos($messageUpper, 'SIGNUP EMAIL:') === 0 && $user->is_whatsapp_guest) {
                $email = trim(substr($messageText, 13)); // Extract email after "SIGNUP EMAIL:"
                return handleSignupWithEmail($user, $email, $phoneNumber);
            } else {
                // Normal message processing
                $chatbotService = app(\App\Services\ChatbotService::class);
                $response = $chatbotService->processMessage($user, $messageText);

                if ($response['success'] && isset($response['bot_message']['message'])) {
                    $botResponse = $response['bot_message']['message'];

                    // Add signup prompt for guest users occasionally
                    if ($user->is_whatsapp_guest && rand(1, 5) === 1) { // 20% chance
                        $botResponse .= "\n\n💡 *Want to save your data and unlock more features?*\nType 'SIGNUP' to create your free Riwe account!";
                    }

                    return $botResponse;
                } else {
                    logMessage("Chatbot processing failed", [
                        'user_id' => $user->id,
                        'error' => $response['error'] ?? 'Unknown error'
                    ]);
                    return null;
                }
            }
        }

    } catch (\Exception $e) {
        logMessage("AI processing error", [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return null;
    }
}

// Handle signup command
function handleSignupCommand($user, $phoneNumber) {
    logMessage("Signup command received", [
        'user_id' => $user->id,
        'phone' => $phoneNumber
    ]);

    $signupMessage = "🚀 *Create Your Riwe Account* 🚀\n\n";
    $signupMessage .= "Great! Let's upgrade your account to unlock all Riwe features.\n\n";
    $signupMessage .= "📧 *Step 1: Provide your email*\n";
    $signupMessage .= "Reply with: `SIGNUP EMAIL: <EMAIL>`\n\n";
    $signupMessage .= "Example: `SIGNUP EMAIL: <EMAIL>`\n\n";
    $signupMessage .= "✅ Benefits of full account:\n";
    $signupMessage .= "• Save your farm data\n";
    $signupMessage .= "• Access insurance services\n";
    $signupMessage .= "• Get loans and financial services\n";
    $signupMessage .= "• Track your crops and yields\n";
    $signupMessage .= "• Receive personalized insights\n\n";
    $signupMessage .= "💡 Your phone number will be verified automatically!";

    return $signupMessage;
}

// Handle signup with email
function handleSignupWithEmail($user, $email, $phoneNumber) {
    try {
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return "❌ Invalid email format. Please try again with a valid email:\n\nExample: `SIGNUP EMAIL: <EMAIL>`";
        }

        // Check if email already exists
        $existingUser = \App\Models\User::where('email', $email)->first();
        if ($existingUser && $existingUser->id !== $user->id) {
            return "❌ This email is already registered. Please use a different email or contact support if this is your email.";
        }

        logMessage("Processing signup with email", [
            'user_id' => $user->id,
            'email' => $email,
            'phone' => $phoneNumber
        ]);

        // Update user to full account
        $user->update([
            'email' => $email,
            'is_whatsapp_guest' => false,
            'email_verified_at' => now(), // Auto-verify since they're using WhatsApp
        ]);

        // Generate a temporary password
        $tempPassword = generateTempPassword();
        $user->update(['password' => bcrypt($tempPassword)]);

        logMessage("User upgraded to full account", [
            'user_id' => $user->id,
            'email' => $email,
            'phone' => $phoneNumber
        ]);

        // Send success message with login details
        $successMessage = "🎉 *Account Created Successfully!* 🎉\n\n";
        $successMessage .= "Welcome to Riwe, {$user->name}!\n\n";
        $successMessage .= "📧 *Your Login Details:*\n";
        $successMessage .= "Email: {$email}\n";
        $successMessage .= "Password: `{$tempPassword}`\n";
        $successMessage .= "Phone: {$phoneNumber}\n\n";
        $successMessage .= "🌐 *Access Your Account:*\n";
        $successMessage .= "Web: https://riwe.io/login\n";
        $successMessage .= "Mobile: Download Riwe app\n\n";
        $successMessage .= "🔒 *Security Tip:* Change your password after first login\n\n";
        $successMessage .= "✅ Your account is now fully activated! You can continue chatting here or log in to the web platform for more features.";

        // Send welcome email (optional)
        try {
            sendWelcomeEmail($user, $tempPassword);
        } catch (\Exception $e) {
            logMessage("Failed to send welcome email", [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        return $successMessage;

    } catch (\Exception $e) {
        logMessage("Signup error", [
            'user_id' => $user->id,
            'email' => $email,
            'error' => $e->getMessage()
        ]);

        return "❌ Sorry, there was an error creating your account. Please try again or contact support.";
    }
}

// Generate temporary password
function generateTempPassword() {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $password = '';
    for ($i = 0; $i < 8; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $password;
}

// Send welcome email (optional)
function sendWelcomeEmail($user, $tempPassword) {
    // This would integrate with your email service
    // For now, just log it
    logMessage("Welcome email would be sent", [
        'user_id' => $user->id,
        'email' => $user->email,
        'temp_password' => $tempPassword
    ]);
}

// INSTANT RESPONSE SYSTEM - Get immediate response for common queries (< 50ms)
function getInstantWhatsAppResponse($messageText, $phoneNumber) {
    $messageLower = strtolower(trim($messageText));

    // Pre-computed instant responses for common queries
    $instantResponses = [
        // Greetings (most common)
        'hello' => "🌾 Hello! I'm Ije, your AI farming assistant from Riwe. How can I help you today?",
        'hi' => "👋 Hi there! I'm Ije from Riwe. What farming question can I help you with?",
        'hey' => "🌱 Hey! I'm here to help with your farming needs. What would you like to know?",
        'good morning' => "🌅 Good morning! Ready to make your farm more productive today?",
        'good afternoon' => "☀️ Good afternoon! How can I assist with your farming today?",
        'good evening' => "🌆 Good evening! What farming topic can I help you with?",

        // Quick farming tips
        'help' => "🌾 *Ije AI Help Menu*\n\n🌱 Ask me about:\n• Crop planting & care\n• Weather forecasts\n• Pest & disease control\n• Market prices\n• Insurance & loans\n• Soil management\n\nJust type your question!",
        'menu' => "📋 *Riwe Services*\n\n🌾 Farming Guidance\n🌤️ Weather Updates\n💰 Market Prices\n🛡️ Insurance Services\n💳 Financial Services\n📊 Farm Analytics\n\nWhat interests you?",

        // Weather (very common)
        'weather' => "🌤️ For accurate weather forecasts, please tell me your location or ask: 'Weather in [your area]'",
        'rain' => "🌧️ Rain predictions depend on your location. Ask: 'Will it rain in [your area]?' for specific forecasts.",

        // Market prices (common)
        'price' => "💰 For current market prices, ask: 'Price of [crop] in [location]' - e.g., 'Price of maize in Nairobi'",
        'market' => "📊 I can help you find current market prices for crops. What crop and location are you interested in?",

        // Quick crop info
        'maize' => "🌽 *Maize Farming Quick Tips*\n\n🌱 Plant during rainy season\n💧 Needs 500-800mm rainfall\n🌡️ Grows best at 18-27°C\n⏰ Harvest in 3-4 months\n\nNeed specific advice? Ask away!",
        'rice' => "🌾 *Rice Farming Basics*\n\n💧 Needs flooded fields\n🌡️ Optimal temp: 20-35°C\n⏰ 3-6 months to harvest\n🌱 Plant in nursery first\n\nWhat specific rice question do you have?",
        'beans' => "🫘 *Bean Farming Tips*\n\n🌱 Plant after last frost\n💧 Moderate water needs\n🌡️ Grows well 15-25°C\n⏰ Ready in 2-3 months\n\nAny specific bean questions?",

        // Insurance & finance
        'insurance' => "🛡️ *Riwe Insurance Services*\n\n✅ Crop insurance\n✅ Livestock coverage\n✅ Weather protection\n✅ Equipment insurance\n\nInterested in coverage? I can connect you with our team on the Riwe platform!",
        'loan' => "💳 *Riwe Financial Services*\n\n💰 Farm loans\n🌾 Input financing\n📱 Mobile payments\n📊 Credit scoring\n\nNeed financing? Let me help you explore options on your dashboard!",

        // Common questions
        'thank you' => "🙏 You're welcome! I'm always here to help with your farming needs. Feel free to ask anything else!",
        'thanks' => "😊 Happy to help! Got more farming questions? I'm here for you 24/7!",
        'bye' => "👋 Goodbye! Remember, I'm here whenever you need farming advice. Have a great day!",
        'goodbye' => "🌾 Take care! Come back anytime for farming guidance. Wishing you a productive harvest!",
    ];

    // Check for exact matches first
    if (isset($instantResponses[$messageLower])) {
        logMessage("INSTANT response found (exact match)", [
            'message' => $messageLower,
            'phone' => $phoneNumber
        ]);
        return $instantResponses[$messageLower];
    }

    // Check for partial matches (contains keywords) - but be more selective
    $safePartialMatches = ['hello', 'hi', 'hey', 'help', 'menu', 'thank you', 'thanks', 'bye', 'goodbye', 'good morning', 'good afternoon', 'good evening'];

    foreach ($instantResponses as $keyword => $response) {
        // Only do partial matching for safe keywords that won't interfere with specific queries
        if (in_array($keyword, $safePartialMatches) && strpos($messageLower, $keyword) !== false) {
            logMessage("INSTANT response found (safe partial match)", [
                'message' => $messageLower,
                'keyword' => $keyword,
                'phone' => $phoneNumber
            ]);
            return $response;
        }
    }

    // Pattern-based fast responses (no AI needed)
    // Check for weather queries - but only give generic response if no location is specified
    if (preg_match('/weather|rain|forecast|temperature|climate/i', $messageText)) {
        // Check if location is specified in the message
        if (preg_match('/weather\s+in\s+\w+|forecast\s+for\s+\w+|rain\s+in\s+\w+/i', $messageText)) {
            // Location-specific weather query - let AI handle it
            return null;
        } else {
            // Generic weather query - provide instant response
            return "🌤️ For accurate weather forecasts, please tell me your location or ask: 'Weather in [your area]'\n\n💡 *Example*: 'Weather in Lugbe' or 'Forecast for Abuja'\n\n🌾 *Farming Tip*: Plan your activities based on weather forecasts to maximize productivity!";
        }
    }

    // Check for market/price queries - but only give generic response if no specific crop/location is mentioned
    if (preg_match('/price|market|cost|sell|buy/i', $messageText)) {
        // Check if specific crop and/or location is mentioned
        if (preg_match('/price\s+of\s+\w+|market\s+price\s+\w+|cost\s+of\s+\w+|\w+\s+price\s+in\s+\w+|\w+\s+market\s+in\s+\w+/i', $messageText)) {
            // Specific market query - let AI handle it
            return null;
        } else {
            // Generic market query - provide instant response
            return "💰 *Market Prices*\n\nI can help you find current market prices. For the most accurate rates:\n\n📍 Specify your location\n🌾 Mention the specific crop\n📅 Current prices vary by region\n\n*Example*: 'Price of maize in Nairobi' or 'Tomato market in Lagos'";
        }
    }

    // Check for farming queries - improved logic to detect specific questions
    if (preg_match('/plant|grow|seed|farming|crop|harvest|disease|pest|fertilizer|irrigation|soil/i', $messageText)) {
        // Check if it's a specific farming question that needs AI processing
        $specificPatterns = [
            // Location-specific questions
            '/\b(in|for|at)\s+(kano|lagos|abuja|kaduna|katsina|jigawa|bauchi|gombe|yobe|borno|adamawa|taraba|plateau|nasarawa|benue|kogi|kwara|oyo|osun|ondo|ekiti|ogun|cross\s+river|akwa\s+ibom|rivers|bayelsa|delta|edo|anambra|imo|abia|ebonyi|enugu|zamfara|kebbi|sokoto|niger|fct)/i',
            // Crop-specific questions
            '/\b(rice|maize|corn|wheat|millet|sorghum|yam|cassava|potato|tomato|pepper|onion|beans|cowpea|groundnut|soybean|cotton|sugarcane|plantain|banana|cocoa|palm|oil\s+palm)\b/i',
            // Specific question words
            '/\b(best|suitable|good|recommend|which|what|when|how|where|tips|advice|guide)\s+(crop|plant|seed|variety|type)/i',
            // Time-specific questions
            '/\b(season|time|month|when)\s+to\s+(plant|grow|sow|harvest)/i',
            // Problem-specific questions
            '/\b(disease|pest|problem|issue|damage|sick|dying|yellow|brown|spots|holes)\b/i'
        ];

        $isSpecificQuestion = false;
        foreach ($specificPatterns as $pattern) {
            if (preg_match($pattern, $messageText)) {
                $isSpecificQuestion = true;
                break;
            }
        }

        if ($isSpecificQuestion) {
            // Specific farming question - let AI handle it
            return null;
        } else {
            // Generic farming query - provide instant response
            return "🌱 *Farming Guidance*\n\nGreat question about farming! Here are key factors for successful planting:\n\n🌧️ **Season**: Plant during optimal weather\n🌡️ **Temperature**: Check crop requirements\n💧 **Water**: Ensure adequate irrigation\n🌱 **Seeds**: Use quality certified seeds\n\nWhat specific crop or farming question do you have?";
        }
    }

    // No instant response available
    logMessage("No instant response available", [
        'message' => $messageLower,
        'phone' => $phoneNumber
    ]);
    return null;
}

// Process image through Ije AI
function processImageWithIjeAI($phoneNumber, $mediaId, $caption, $userName) {
    try {
        logMessage("Starting image analysis", [
            'phone' => $phoneNumber,
            'media_id' => $mediaId,
            'caption' => $caption,
            'user_name' => $userName
        ]);

        // Load Laravel to access services
        require_once __DIR__ . '/../vendor/autoload.php';
        $app = require_once __DIR__ . '/../bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

        // Find or create WhatsApp user
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phoneNumber);
        $user = \App\Models\User::where('phone_number', $cleanPhone)
            ->orWhere('phone_number', $phoneNumber)
            ->first();

        if (!$user) {
            // Create new WhatsApp guest user
            $user = \App\Models\User::create([
                'name' => $userName,
                'email' => 'whatsapp_' . $cleanPhone . '@guest.riwe.io',
                'phone_number' => $cleanPhone,
                'password' => bcrypt(\Illuminate\Support\Str::random(32)),
                'is_whatsapp_guest' => true,
                'email_verified_at' => now(),
                'preferred_currency' => 'NGN',
                'location' => 'Nigeria',
            ]);

            // Assign default role
            $defaultRole = \App\Models\Role::where('name', 'user')->first();
            if ($defaultRole) {
                $user->roles()->attach($defaultRole->id);
            }

            logMessage("Created new WhatsApp user for image analysis", [
                'user_id' => $user->id,
                'phone' => $cleanPhone,
                'name' => $userName
            ]);
        }

        // Download the image from WhatsApp
        $downloadResult = downloadWhatsAppMedia($mediaId);

        if (!$downloadResult['success']) {
            logMessage("Failed to download WhatsApp media", [
                'media_id' => $mediaId,
                'error' => $downloadResult['error']
            ]);
            return null;
        }

        $imagePath = $downloadResult['path'];

        logMessage("Image downloaded successfully", [
            'media_id' => $mediaId,
            'path' => $imagePath,
            'size' => filesize($imagePath)
        ]);

        // Analyze the image using the existing service
        $pestDetectionService = app(\App\Services\PestDiseaseDetectionService::class);
        $analysisResult = $pestDetectionService->analyzeImage($imagePath, $caption, $user);

        // Clean up the downloaded file
        if (file_exists($imagePath)) {
            unlink($imagePath);
            logMessage("Cleaned up temporary image file", ['path' => $imagePath]);
        }

        if ($analysisResult['success']) {
            // Format the response for WhatsApp
            $formattedResponse = formatImageAnalysisForWhatsApp($analysisResult);

            logMessage("Image analysis completed successfully", [
                'user_id' => $user->id,
                'confidence' => $analysisResult['confidence'] ?? 0,
                'response_length' => strlen($formattedResponse)
            ]);

            return $formattedResponse;
        } else {
            logMessage("Image analysis failed", [
                'user_id' => $user->id,
                'error' => $analysisResult['error'] ?? 'Unknown error'
            ]);
            return null;
        }

    } catch (\Exception $e) {
        logMessage("Image processing error", [
            'media_id' => $mediaId,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return null;
    }
}

// Download media from WhatsApp
function downloadWhatsAppMedia($mediaId) {
    try {
        $accessToken = 'EAAMse8zYnIgBOZB5ndUzNyULm0TXTNoLbTII7rbxwvf294wVjlhexnToXMaRvBJYaNzBY5suAqExEIrqQXGBHG5E2WzCBg2s8ApmFpbZAfPZADRff02nV7UfjI2UQwtjBpdJIG0sISRuYMthZClDkoWIgFz6yEpNwvxRU5ZAKIZAab1Uh9HG2XdC3hMlqTLNtVFwZDZD';
        $baseUrl = 'https://graph.facebook.com/v18.0';

        // Step 1: Get media URL
        $mediaUrl = "{$baseUrl}/{$mediaId}";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $mediaUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($httpCode !== 200 || $error) {
            return [
                'success' => false,
                'error' => "Failed to get media info: HTTP {$httpCode}, Error: {$error}"
            ];
        }

        $mediaData = json_decode($response, true);
        $downloadUrl = $mediaData['url'] ?? null;

        if (!$downloadUrl) {
            return [
                'success' => false,
                'error' => 'Media URL not found in response'
            ];
        }

        // Step 2: Download the actual media file
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $downloadUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $fileContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($httpCode !== 200 || $error) {
            return [
                'success' => false,
                'error' => "Failed to download media file: HTTP {$httpCode}, Error: {$error}"
            ];
        }

        // Step 3: Save the file temporarily
        $fileName = 'whatsapp_' . $mediaId . '_' . time() . '.jpg';
        $tempDir = __DIR__ . '/../storage/app/temp/whatsapp/';

        // Create directory if it doesn't exist
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $tempPath = $tempDir . $fileName;
        file_put_contents($tempPath, $fileContent);

        logMessage("WhatsApp media downloaded successfully", [
            'media_id' => $mediaId,
            'file_path' => $tempPath,
            'file_size' => filesize($tempPath)
        ]);

        return [
            'success' => true,
            'path' => $tempPath,
            'size' => filesize($tempPath)
        ];

    } catch (\Exception $e) {
        logMessage("Error downloading WhatsApp media", [
            'media_id' => $mediaId,
            'error' => $e->getMessage()
        ]);

        return [
            'success' => false,
            'error' => 'Download failed: ' . $e->getMessage()
        ];
    }
}

// Format image analysis result for WhatsApp
function formatImageAnalysisForWhatsApp($analysisResult) {
    try {
        $message = "🔍 *Ije's Plant Analysis* 🌱\n\n";

        // Add confidence level
        if (isset($analysisResult['confidence'])) {
            $confidence = $analysisResult['confidence'] * 100;
            $message .= "📊 *Confidence:* {$confidence}%\n\n";
        }

        // Add main analysis message
        if (isset($analysisResult['message'])) {
            // Clean up the message for WhatsApp (remove excessive formatting)
            $cleanMessage = $analysisResult['message'];
            $cleanMessage = preg_replace('/\*\*(.*?)\*\*/', '*$1*', $cleanMessage); // Convert ** to *
            $cleanMessage = preg_replace('/#{1,6}\s/', '', $cleanMessage); // Remove markdown headers
            $cleanMessage = str_replace(['🔍 **Ije\'s Analysis** 🎯', '(Very High Confidence)'], '', $cleanMessage);
            $cleanMessage = trim($cleanMessage);

            $message .= $cleanMessage . "\n\n";
        }

        // Add structured analysis if available
        if (isset($analysisResult['analysis']) && is_array($analysisResult['analysis'])) {
            $analysis = $analysisResult['analysis'];

            if (isset($analysis['plant_type'])) {
                $message .= "🌿 *Plant:* " . $analysis['plant_type'] . "\n";
            }

            if (isset($analysis['health_status'])) {
                $message .= "💚 *Health:* " . $analysis['health_status'] . "\n";
            }

            if (isset($analysis['severity_level'])) {
                $message .= "⚠️ *Severity:* " . $analysis['severity_level'] . "\n";
            }

            $message .= "\n";
        }

        // Add footer
        $message .= "💡 *Need more help?* Send another image or ask me questions!\n";
        $message .= "🌾 *Powered by Ije AI - Your Farming Assistant*";

        // Ensure message is not too long for WhatsApp (4096 character limit)
        if (strlen($message) > 4000) {
            $message = substr($message, 0, 3950) . "...\n\n💡 *Full analysis available on Riwe platform*";
        }

        return $message;

    } catch (\Exception $e) {
        logMessage("Error formatting WhatsApp analysis response", [
            'error' => $e->getMessage(),
            'analysis_result' => $analysisResult
        ]);

        return "🔍 *Analysis Complete* 🌱\n\nI've analyzed your plant image. " .
               "For detailed results, please visit the Riwe platform or try sending the image again.";
    }
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

logMessage("Webhook called", [
    'method' => $method,
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'timestamp' => date('c')
]);

if ($method === 'GET') {
    // Handle webhook verification
    $mode = $_GET['hub_mode'] ?? '';
    $challenge = $_GET['hub_challenge'] ?? '';
    $token = $_GET['hub_verify_token'] ?? '';

    logMessage("Webhook verification attempt", [
        'mode' => $mode,
        'challenge' => $challenge,
        'token' => $token
    ]);

    if ($mode === 'subscribe' && $token === 'riwe_webhook_verify_2024') {
        logMessage("Webhook verification successful");
        header('Content-Type: text/plain');
        echo $challenge;
        exit;
    } else {
        logMessage("Webhook verification failed");
        http_response_code(403);
        echo "Verification failed";
        exit;
    }
}

if ($method === 'POST') {
    // Handle incoming messages
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    logMessage("Webhook POST received", [
        'payload' => $data,
        'raw_input' => $input
    ]);

    if ($data && isset($data['entry'])) {
        foreach ($data['entry'] as $entry) {
            if (isset($entry['changes'])) {
                foreach ($entry['changes'] as $change) {
                    if ($change['field'] === 'messages' && isset($change['value']['messages'])) {
                        foreach ($change['value']['messages'] as $message) {
                            $phoneNumber = $message['from'] ?? null;
                            $messageType = $message['type'] ?? 'unknown';
                            $userName = $change['value']['contacts'][0]['profile']['name'] ?? 'WhatsApp User';

                            logMessage("WhatsApp message received", [
                                'from' => $phoneNumber,
                                'type' => $messageType,
                                'message_id' => $message['id'] ?? null
                            ]);

                            if (!$phoneNumber) {
                                logMessage("Missing phone number in message", ['message' => $message]);
                                continue;
                            }

                            // Handle different message types
                            if ($messageType === 'text') {
                                // Handle text messages with INSTANT RESPONSE SYSTEM
                                $messageText = $message['text']['body'] ?? null;
                                $messageId = $message['id'] ?? null;

                                if ($messageText) {
                                    logMessage("Processing text message with instant response", [
                                        'from' => $phoneNumber,
                                        'message' => $messageText,
                                        'message_id' => $messageId
                                    ]);

                                    // Mark message as read
                                    if ($messageId) {
                                        markWhatsAppMessageAsRead($phoneNumber, $messageId);
                                    }

                                    // INSTANT RESPONSE SYSTEM - Get immediate response
                                    $instantResponse = getInstantWhatsAppResponse($messageText, $phoneNumber);

                                    if ($instantResponse) {
                                        // Send instant response immediately (< 50ms)
                                        $sent = sendWhatsAppMessage($phoneNumber, $instantResponse);
                                        logMessage("INSTANT Response sent", [
                                            'to' => $phoneNumber,
                                            'success' => $sent,
                                            'response_length' => strlen($instantResponse),
                                            'type' => 'instant'
                                        ]);
                                    } else {
                                        // Send immediate acknowledgment for complex queries
                                        sendWhatsAppMessage($phoneNumber, "🌾 *Ije AI*: Let me help you with that...");

                                        // Process with AI in background
                                        $responseMessage = processWithIjeAI($phoneNumber, $messageText, $userName);

                                        if ($responseMessage) {
                                            $sent = sendWhatsAppMessage($phoneNumber, $responseMessage);
                                            logMessage("AI Response sent", [
                                                'to' => $phoneNumber,
                                                'success' => $sent,
                                                'response_length' => strlen($responseMessage),
                                                'type' => 'ai_processed'
                                            ]);
                                        } else {
                                            // Fallback response if AI fails
                                            $fallbackMessage = "🌾 Hello! I'm Ije AI from Riwe. I'm experiencing some technical difficulties right now, but I'm here to help with your farming needs. Please try again in a moment.";
                                            sendWhatsAppMessage($phoneNumber, $fallbackMessage);
                                            logMessage("Fallback response sent", [
                                                'to' => $phoneNumber,
                                                'reason' => 'AI processing failed'
                                            ]);
                                        }
                                    }
                                }
                            } elseif ($messageType === 'image') {
                                // Handle image messages
                                $imageData = $message['image'] ?? null;
                                $messageId = $message['id'] ?? null;

                                if ($imageData) {
                                    $mediaId = $imageData['id'] ?? null;
                                    $caption = $imageData['caption'] ?? 'Please analyze this plant image for pests and diseases';

                                    logMessage("Processing image message", [
                                        'from' => $phoneNumber,
                                        'media_id' => $mediaId,
                                        'caption' => $caption,
                                        'message_id' => $messageId
                                    ]);

                                    // Mark message as read
                                    if ($messageId) {
                                        markWhatsAppMessageAsRead($phoneNumber, $messageId);
                                    }

                                    if ($mediaId) {
                                        // Send immediate acknowledgment
                                        sendWhatsAppMessage($phoneNumber, "📸 Image received! 🌱");

                                        // Send thinking message for image analysis
                                        sendWhatsAppThinkingMessage($phoneNumber, 'image');

                                        // Process the image
                                        $analysisResult = processImageWithIjeAI($phoneNumber, $mediaId, $caption, $userName);

                                        if ($analysisResult) {
                                            sendWhatsAppMessage($phoneNumber, $analysisResult);
                                            logMessage("Image analysis response sent", [
                                                'to' => $phoneNumber,
                                                'response_length' => strlen($analysisResult)
                                            ]);
                                        } else {
                                            sendWhatsAppMessage($phoneNumber, "❌ Sorry, I couldn't analyze your image. Please try again later.");
                                            logMessage("Image analysis failed", ['to' => $phoneNumber]);
                                        }
                                    } else {
                                        sendWhatsAppMessage($phoneNumber, "❌ Sorry, I couldn't access your image. Please try sending it again.");
                                        logMessage("Missing media ID in image message", ['image_data' => $imageData]);
                                    }
                                } else {
                                    sendWhatsAppMessage($phoneNumber, "❌ Sorry, I couldn't process your image. Please try sending it again.");
                                    logMessage("Missing image data in image message", ['message' => $message]);
                                }
                            } else {
                                // Handle unsupported message types
                                $supportedTypes = [
                                    'audio' => "🎵 I can't analyze audio messages. Please send an image of your plant for analysis.",
                                    'video' => "🎥 I can't analyze videos yet. Please send a clear image of your plant for analysis.",
                                    'document' => "📄 I can't analyze documents yet. Please send an image of your plant for analysis.",
                                    'sticker' => "😊 Thanks for the sticker! Please send an image of your plant if you need analysis.",
                                    'location' => "📍 Thanks for sharing your location! Please send an image of your plant for analysis."
                                ];

                                $responseMessage = $supportedTypes[$messageType] ??
                                                 "❓ I can only analyze text messages and plant images. Please send an image of your plant for analysis.";

                                sendWhatsAppMessage($phoneNumber, $responseMessage);
                                logMessage("Unsupported message type handled", [
                                    'type' => $messageType,
                                    'from' => $phoneNumber
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode(['status' => 'success', 'received' => true]);
    exit;
}

// Handle other methods
logMessage("Unsupported method", ['method' => $method]);
http_response_code(405);
echo "Method not allowed";
exit;
