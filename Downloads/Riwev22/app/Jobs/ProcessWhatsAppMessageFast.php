<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\WhatsAppService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ProcessWhatsAppMessageFast implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 10; // Ultra-fast timeout
    public $tries = 1; // Single attempt for speed
    public $maxExceptions = 1;

    protected $userId;
    protected $messageText;
    protected $phoneNumber;

    /**
     * Create a new job instance.
     */
    public function __construct(int $userId, string $messageText, string $phoneNumber)
    {
        $this->userId = $userId;
        $this->messageText = $messageText;
        $this->phoneNumber = $phoneNumber;
        
        // Set to high priority queue for fastest processing
        $this->onQueue('high');
    }

    /**
     * Execute the job with ultra-fast processing.
     */
    public function handle(WhatsAppService $whatsappService): void
    {
        try {
            $startTime = microtime(true);
            
            $user = User::find($this->userId);
            
            if (!$user) {
                Log::error('User not found for fast WhatsApp processing', ['user_id' => $this->userId]);
                return;
            }

            // Get fast AI response using optimized approach
            $response = $this->getFastAIResponse($this->messageText, $user);
            
            if ($response) {
                // Send the response immediately
                $sendResult = $whatsappService->sendMessage($this->phoneNumber, $response);
                
                $processingTime = round((microtime(true) - $startTime) * 1000, 2);
                
                if ($sendResult['success']) {
                    Log::info('Fast WhatsApp response sent', [
                        'user_id' => $this->userId,
                        'phone' => $this->phoneNumber,
                        'processing_time_ms' => $processingTime,
                        'response_length' => strlen($response)
                    ]);
                } else {
                    Log::error('Failed to send fast WhatsApp response', [
                        'user_id' => $this->userId,
                        'phone' => $this->phoneNumber,
                        'processing_time_ms' => $processingTime,
                        'error' => $sendResult['message']
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Exception in fast WhatsApp processing', [
                'user_id' => $this->userId,
                'phone' => $this->phoneNumber,
                'error' => $e->getMessage()
            ]);
            
            // Send simple error response
            try {
                $errorMessage = "🌾 I'm processing your request. Please wait a moment...";
                $whatsappService->sendMessage($this->phoneNumber, $errorMessage);
            } catch (\Exception $sendException) {
                Log::error('Failed to send fast error message', [
                    'phone' => $this->phoneNumber,
                    'error' => $sendException->getMessage()
                ]);
            }
        }
    }

    /**
     * Get fast AI response using optimized methods.
     */
    private function getFastAIResponse(string $message, User $user): ?string
    {
        // Check cache first (fastest - ~1ms)
        $cacheKey = "fast_ai_response_" . md5($message . $user->location);
        $cached = Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }

        // Pattern-based responses for speed (no AI needed)
        $response = $this->getPatternBasedResponse($message, $user);
        if ($response) {
            Cache::put($cacheKey, $response, now()->addMinutes(30));
            return $response;
        }

        // Fast OpenAI call with minimal context
        $response = $this->getFastOpenAIResponse($message, $user);
        if ($response) {
            Cache::put($cacheKey, $response, now()->addMinutes(15));
            return $response;
        }

        return null;
    }

    /**
     * Get pattern-based response without AI (ultra-fast).
     */
    private function getPatternBasedResponse(string $message, User $user): ?string
    {
        $messageLower = strtolower($message);
        
        // Weather patterns - only provide generic response if no location specified
        if (preg_match('/weather|rain|forecast|temperature|climate/i', $message)) {
            // Check if location is specified in the message
            if (preg_match('/weather\s+in\s+\w+|forecast\s+for\s+\w+|rain\s+in\s+\w+/i', $message)) {
                // Location-specific weather query - return null to let AI handle it
                return null;
            } else {
                // Generic weather query - provide instant response
                return "🌤️ For accurate weather forecasts, please tell me your location or ask: 'Weather in [your area]'\n\n💡 *Example*: 'Weather in Lugbe' or 'Forecast for Abuja'\n\n🌾 *Farming Tip*: Plan your activities based on weather forecasts to maximize productivity!";
            }
        }
        
        // Price patterns
        if (preg_match('/price|market|cost|sell|buy/i', $message)) {
            return "💰 *Market Prices*\n\nI can help you find current market prices. For the most accurate rates:\n\n📍 Specify your location\n🌾 Mention the specific crop\n📅 Current prices vary by region\n\n*Example*: 'Price of maize in Nairobi'";
        }
        
        // Agriculture patterns - comprehensive global coverage
        $agricultureKeywords = [
            // Core farming terms
            'plant', 'grow', 'seed', 'farming', 'crop', 'harvest', 'agriculture', 'agricultural',
            // Crops and plants
            'rice', 'maize', 'corn', 'wheat', 'millet', 'sorghum', 'yam', 'cassava', 'potato', 'tomato',
            'pepper', 'onion', 'beans', 'cowpea', 'groundnut', 'soybean', 'cotton', 'sugarcane',
            'plantain', 'banana', 'cocoa', 'palm', 'coffee', 'tea', 'barley', 'oats',
            'vegetables', 'fruits', 'cereals', 'legumes', 'tubers', 'spices', 'herbs',
            // Soil and fertilizers
            'soil', 'fertilizer', 'fertiliser', 'compost', 'manure', 'nutrient', 'nitrogen', 'phosphorus', 'potassium',
            // Pests and diseases
            'pest', 'disease', 'insect', 'bug', 'fungus', 'virus', 'bacteria', 'blight', 'rot', 'wilt',
            // Water and irrigation
            'irrigation', 'water', 'drought', 'rainfall', 'watering', 'sprinkler', 'drip',
            // Farm management
            'farm', 'farmer', 'field', 'garden', 'greenhouse', 'nursery', 'orchard', 'plantation',
            // Agricultural practices
            'planting', 'sowing', 'transplanting', 'pruning', 'weeding', 'mulching', 'tilling',
            'cultivation', 'harvesting', 'threshing', 'winnowing', 'storage', 'processing',
            // Modern agriculture
            'hydroponic', 'hydroponics', 'aeroponic', 'vertical farming', 'precision agriculture',
            // Agricultural advisory
            'extension', 'advisory', 'guidance', 'recommendation', 'consultation', 'training',
            // Risk management
            'risk', 'mitigation', 'management', 'warning', 'alert', 'forecast', 'climate',
            // Livestock (basic)
            'livestock', 'cattle', 'poultry', 'chicken', 'goat', 'sheep', 'pig', 'fish', 'aquaculture'
        ];

        // Check if message contains any agriculture-related keywords
        $isAgricultureQuery = false;
        foreach ($agricultureKeywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                $isAgricultureQuery = true;
                break;
            }
        }

        if ($isAgricultureQuery) {
            // ALL agriculture-related questions go to AI for comprehensive responses
            return null;
        }

        
        // Insurance patterns
        if (preg_match('/insurance|cover|protect|risk|loss/i', $message)) {
            return "🛡️ *Riwe Insurance Services*\n\n✅ **Crop Insurance**: Protect against weather risks\n✅ **Livestock Coverage**: Secure your animals\n✅ **Equipment Protection**: Cover farm machinery\n✅ **Income Protection**: Safeguard your livelihood\n\n💡 *Get a quote today!* Insurance protects your investment.";
        }
        
        // Loan/finance patterns
        if (preg_match('/loan|money|finance|credit|fund|capital/i', $message)) {
            return "💳 *Riwe Financial Services*\n\n💰 **Farm Loans**: Competitive rates\n🌾 **Input Financing**: Seeds, fertilizer, equipment\n📱 **Mobile Banking**: Easy payments\n📊 **Credit Building**: Improve your score\n\n🚀 *Apply now* for fast approval!";
        }
        
        // Update/news patterns
        if (preg_match('/update|news|latest|current|recent/i', $message)) {
            return "📰 *Latest Farming Updates*\n\n🌾 **Seasonal Tips**: Best practices for current season\n💰 **Market Trends**: Price movements and opportunities\n🌤️ **Weather Alerts**: Important climate updates\n🆕 **New Services**: Latest Riwe offerings\n\nWhat specific updates interest you?";
        }

        return null;
    }

    /**
     * Get fast OpenAI response with minimal context.
     */
    private function getFastOpenAIResponse(string $message, User $user): ?string
    {
        try {
            $apiKey = config('services.openai.api_key');
            if (!$apiKey) {
                return null;
            }

            // Ultra-minimal prompt for speed
            $prompt = "You are Ije, a farming AI assistant from Riwe. Give a helpful, concise response (max 200 words) about: {$message}";
            
            $payload = [
                'model' => 'gpt-3.5-turbo', // Faster than GPT-4
                'messages' => [
                    ['role' => 'system', 'content' => $prompt],
                    ['role' => 'user', 'content' => $message]
                ],
                'max_tokens' => 150, // Limit for speed
                'temperature' => 0.7,
                'stream' => false
            ];

            $response = Http::timeout(8) // Ultra-fast timeout
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ])->post('https://api.openai.com/v1/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();
                $content = $data['choices'][0]['message']['content'] ?? null;
                
                if ($content) {
                    return "🌾 *Ije AI*\n\n" . trim($content);
                }
            }

        } catch (\Exception $e) {
            Log::warning('Fast OpenAI call failed', [
                'error' => $e->getMessage(),
                'message' => substr($message, 0, 50)
            ]);
        }

        return null;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Fast WhatsApp processing job failed', [
            'user_id' => $this->userId,
            'phone' => $this->phoneNumber,
            'error' => $exception->getMessage()
        ]);
        
        // Send simple fallback message
        try {
            $whatsappService = app(WhatsAppService::class);
            $fallbackMessage = "🌾 Thanks for your message! I'm here to help with farming. Please try rephrasing your question.";
            $whatsappService->sendMessage($this->phoneNumber, $fallbackMessage);
        } catch (\Exception $e) {
            Log::error('Failed to send fast fallback message', [
                'phone' => $this->phoneNumber,
                'error' => $e->getMessage()
            ]);
        }
    }
}
