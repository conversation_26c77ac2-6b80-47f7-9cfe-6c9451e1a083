<?php

namespace App\Services;

use App\Models\ChatbotConfiguration;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private ?string $apiKey;
    private string $model;
    private float $temperature;
    private int $maxTokens;

    public function __construct()
    {
        $this->apiKey = ChatbotConfiguration::getValue('openai_api_key', '') ?: env('OPENAI_API_KEY');
        $this->model = ChatbotConfiguration::getValue('openai_model', 'gpt-4o-mini');
        $this->temperature = (float) ChatbotConfiguration::getValue('openai_temperature', 0.7);
        $this->maxTokens = (int) ChatbotConfiguration::getValue('openai_max_tokens', 1000);
    }

    /**
     * Send a message to OpenAI and get response.
     */
    public function sendMessage(string $message, array $conversationHistory = [], array $context = []): array
    {
        try {
            // Extract location from message for any location-based query
            $extractedLocation = $this->extractAnyLocationFromMessage($message);

            // Check if this is a location-based crop recommendation query
            $locationCropService = app(\App\Services\LocationCropRecommendationService::class);
            $locationCropData = null;

            if ($this->isLocationCropQuery($message)) {
                $location = $extractedLocation ?: $this->extractLocationFromMessage($message);

                if ($location) {
                    Log::info('Location-based crop query detected', [
                        'message' => $message,
                        'location' => $location
                    ]);

                    $recommendations = $locationCropService->getLocationCropRecommendations($location);

                    if ($recommendations) {
                        // Add location crop recommendations to context
                        $context['location_crop_recommendations'] = [
                            'location' => $location,
                            'recommendations' => $recommendations,
                            'type' => 'location_crop_query'
                        ];
                        Log::info('Location crop recommendations found', [
                            'location' => $location,
                            'primary_crops_count' => count($recommendations['primary'] ?? []),
                            'secondary_crops_count' => count($recommendations['secondary'] ?? [])
                        ]);
                    }
                }
            }

            // Add general location context if location was extracted
            if ($extractedLocation) {
                $context['query_location'] = [
                    'location' => $extractedLocation,
                    'type' => $this->detectQueryType($message),
                    'is_weather_query' => $this->isWeatherQuery($message),
                    'is_market_query' => $this->isMarketQuery($message)
                ];

                Log::info('Location extracted from query', [
                    'message' => $message,
                    'location' => $extractedLocation,
                    'query_type' => $context['query_location']['type']
                ]);
            }

            // Check if this is a market price query and perform web search if needed
            $webSearchService = app(\App\Services\WebSearchService::class);
            $webSearchData = null;

            if ($webSearchService->isMarketPriceQuery($message)) {
                $commodity = $webSearchService->extractCommodity($message);

                // Extract country from context or default to Nigeria
                $country = $this->extractCountryFromContext($context) ?? 'Nigeria';

                if ($commodity) {
                    Log::info('Specific commodity price query detected', [
                        'message' => $message,
                        'commodity' => $commodity,
                        'country' => $country
                    ]);

                    // Perform web search for specific commodity prices
                    $webSearchData = $webSearchService->searchCommodityPrices($commodity, $country);

                    if ($webSearchData['success']) {
                        // Add web search results to context
                        $context['web_search_results'] = $webSearchData;
                        Log::info('Web search successful for specific commodity prices', [
                            'commodity' => $commodity,
                            'country' => $country,
                            'sources_count' => count($webSearchData['sources'] ?? [])
                        ]);
                    }
                } else {
                    // General market price query without specific commodity
                    Log::info('General market price query detected', [
                        'message' => $message,
                        'country' => $country
                    ]);

                    // Perform general market search
                    $webSearchData = $webSearchService->searchGeneralMarketInfo($country);

                    if ($webSearchData['success']) {
                        // Add web search results to context
                        $context['web_search_results'] = $webSearchData;
                        Log::info('Web search successful for general market info', [
                            'country' => $country,
                            'sources_count' => count($webSearchData['sources'] ?? [])
                        ]);
                    }
                }
            }

            // Build the conversation messages
            $messages = $this->buildMessages($message, $conversationHistory, $context);

            // Prepare the request payload
            $payload = [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => $this->temperature,
                'max_tokens' => $this->maxTokens,
                'presence_penalty' => 0.1,
                'frequency_penalty' => 0.1,
            ];

            // Make the API request
            $response = $this->makeApiRequest($payload);

            $processedResponse = $this->processResponse($response);

            // Add web search metadata if available
            if ($webSearchData && $webSearchData['success']) {
                $processedResponse['web_search_used'] = true;
                $processedResponse['web_search_sources'] = $webSearchData['sources'] ?? [];
                $processedResponse['web_search_commodity'] = $webSearchData['commodity'] ?? null;
                $processedResponse['web_search_country'] = $webSearchData['country'] ?? null;
                $processedResponse['web_search_type'] = $webSearchData['type'] ?? 'commodity';

                // Add metadata for analytics tracking
                $processedResponse['metadata'] = array_merge($processedResponse['metadata'] ?? [], [
                    'web_search_used' => true,
                    'web_search_sources' => $webSearchData['sources'] ?? [],
                    'web_search_commodity' => $webSearchData['commodity'] ?? null,
                    'web_search_country' => $webSearchData['country'] ?? null,
                    'web_search_type' => $webSearchData['type'] ?? 'commodity',
                    'web_search_query' => $webSearchData['search_query'] ?? null,
                    'web_search_timestamp' => now()->toIso8601String()
                ]);
            }

            return $processedResponse;

        } catch (\Exception $e) {
            Log::error('OpenAI API error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'I apologize, but I\'m experiencing some technical difficulties right now. Please try again in a moment.',
                'confidence' => 0,
                'intent' => null,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Analyze an image with GPT-4 Vision for pest and disease detection.
     */
    public function analyzeImageForPestDisease(string $imagePath, string $userMessage = ''): array
    {
        try {
            // Get the configured model with fallback to working vision models
            $configuredModel = $this->getConfig('openai_model', 'gpt-4o-mini');

            // Available vision models (in order of preference)
            $visionModels = [
                'gpt-4o',                // Latest and most capable
                'gpt-4o-mini',          // Faster and cheaper
                'gpt-4-vision-preview'  // Original vision model
            ];

            // Use configured model if it's a vision model, otherwise use best available
            $visionModel = in_array($configuredModel, $visionModels) ? $configuredModel : 'gpt-4o-mini';

            // Convert image to base64 and detect MIME type
            \Log::info('Converting image to base64', ['image_path' => $imagePath]);
            $imageData = $this->encodeImageToBase64($imagePath);
            $mimeType = $this->getImageMimeType($imagePath);
            \Log::info('Image converted successfully', [
                'base64_length' => strlen($imageData),
                'mime_type' => $mimeType,
                'base64_preview' => substr($imageData, 0, 50) . '...'
            ]);

            // Build specialized prompt for agricultural analysis
            $prompt = $this->buildPestDiseasePrompt($userMessage);

            // Prepare the vision request payload
            $payload = [
                'model' => $visionModel,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $prompt
                            ],
                            [
                                'type' => 'image_url',
                                'image_url' => [
                                    'url' => "data:{$mimeType};base64,{$imageData}",
                                    'detail' => 'high'
                                ]
                            ]
                        ]
                    ]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.3, // Lower temperature for more consistent analysis
            ];

            // Make the API request
            \Log::info('Sending vision request to OpenAI', [
                'model' => $visionModel,
                'prompt_length' => strlen($prompt),
                'has_image_data' => !empty($imageData)
            ]);

            $response = $this->makeApiRequest($payload);

            \Log::info('Received response from OpenAI', [
                'has_choices' => !empty($response['choices']),
                'choice_count' => count($response['choices'] ?? []),
                'usage' => $response['usage'] ?? null
            ]);

            return $this->processVisionResponse($response);

        } catch (\Exception $e) {
            Log::error('OpenAI Vision API error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'I apologize, but I\'m having trouble analyzing the image right now. Please try again in a moment.',
                'confidence' => 0,
                'analysis' => null,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Make API request to OpenAI.
     */
    private function makeApiRequest(array $payload): array
    {
        if (empty($this->apiKey) || $this->apiKey === null) {
            throw new \Exception('OpenAI API key not configured');
        }

        // Reduced timeout for faster response - WhatsApp users expect quick replies
        $timeout = (int) ChatbotConfiguration::getValue('openai_timeout', 20);
        $response = Http::timeout($timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post('https://api.openai.com/v1/chat/completions', $payload);

        if (!$response->successful()) {
            throw new \Exception('OpenAI API request failed: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Build messages array for OpenAI API.
     */
    private function buildMessages(string $message, array $conversationHistory, array $context): array
    {
        $messages = [];

        // System message with Ije's personality and farming expertise
        $systemMessage = $this->buildSystemMessage($context);
        $messages[] = ['role' => 'system', 'content' => $systemMessage];

        // Add conversation history (last 5 messages for faster response)
        $recentHistory = array_slice($conversationHistory, -5);
        foreach ($recentHistory as $historyMessage) {
            $messages[] = [
                'role' => $historyMessage['role'],
                'content' => $historyMessage['content'],
            ];
        }

        // Add current user message
        $messages[] = ['role' => 'user', 'content' => $message];

        return $messages;
    }

    /**
     * Build system message with Ije's personality and context.
     */
    private function buildSystemMessage(array $context): string
    {
        // Debug: Log what context is being received
        \Log::info('OpenAI: Building system message with context', [
            'has_user_context' => !empty($context['user_context']),
            'has_farm_data' => !empty($context['farm_data']),
            'farm_data_keys' => !empty($context['farm_data']) ? array_keys($context['farm_data']) : [],
            'farms_count' => !empty($context['farm_data']['farms']) ? count($context['farm_data']['farms']) : 0,
        ]);

        $systemMessage = "You are Ije, a friendly and knowledgeable AI farming assistant for Riwe, a pioneering agricultural insurance technology platform with GLOBAL coverage. ";
        $systemMessage .= "Your name 'Ije' means 'journey' in Igbo, representing the farming journey you help users navigate WORLDWIDE.\n\n";

        $systemMessage .= "CRITICAL LOCATION HANDLING RULES:\n";
        $systemMessage .= "🌍 **GLOBAL COVERAGE**: You provide agricultural assistance for ANY location worldwide\n";
        $systemMessage .= "📍 **LOCATION ACCURACY**: ALWAYS respond about the SPECIFIC location mentioned in the user's query\n";
        $systemMessage .= "🚫 **NO DEFAULT ASSUMPTIONS**: Do NOT assume or default to Nigeria unless specifically mentioned\n";
        $systemMessage .= "🎯 **EXACT LOCATION MATCHING**: If user asks about Dublin, respond about Dublin (not Nairobi or any other city)\n";
        $systemMessage .= "🌤️ **WEATHER QUERIES**: Provide weather for the EXACT location requested (e.g., Dublin weather for Dublin query)\n";
        $systemMessage .= "💰 **MARKET QUERIES**: Provide market information for the SPECIFIC region mentioned\n";
        $systemMessage .= "🌾 **CROP RECOMMENDATIONS**: Consider the ACTUAL climate and conditions of the mentioned location\n\n";

        $systemMessage .= "ABOUT RIWE PLATFORM:\n";
        $systemMessage .= "Riwe is a comprehensive agricultural technology platform that transforms agricultural risk management through advanced technology. ";
        $systemMessage .= "We combine data analytics, satellite imagery, weather forecasting, and parametric insurance models to help farmers manage climate and agricultural risks.\n\n";

        $systemMessage .= "RIWE'S CORE SERVICES:\n";
        $systemMessage .= "• **Agricultural Insurance**: Parametric crop insurance, weather-based insurance, livestock insurance, and equipment protection\n";
        $systemMessage .= "• **Farm Management Tools**: Comprehensive farm data management, crop tracking, yield monitoring, and performance analytics\n";
        $systemMessage .= "• **Weather & Climate Services**: Real-time weather monitoring, 7-day forecasts, climate risk assessment, and severe weather alerts\n";
        $systemMessage .= "• **Financial Services**: Agricultural loans, microfinance, investment planning, and financial risk assessment\n";
        $systemMessage .= "• **Market Intelligence**: Crop price monitoring, market trends analysis, supply chain insights, and trading recommendations\n";
        $systemMessage .= "• **Real-Time Market Prices**: Access to current agricultural commodity prices through web search integration\n";
        $systemMessage .= "• **Pest & Disease Detection**: AI-powered image analysis for crop health assessment and treatment recommendations\n";
        $systemMessage .= "• **Soil & Land Management**: Soil health analysis, fertility recommendations, and land optimization strategies\n";
        $systemMessage .= "• **Digital Identity & Cards**: Farmer verification, digital identity management, and access to financial services\n\n";

        $systemMessage .= "RIWE'S TECHNOLOGY FEATURES:\n";
        $systemMessage .= "• **Satellite Monitoring**: Real-time crop monitoring using satellite imagery and remote sensing\n";
        $systemMessage .= "• **Machine Learning & AI**: Predictive analytics, computer vision for crop analysis, and intelligent recommendations\n";
        $systemMessage .= "• **Mobile & Web Platform**: User-friendly interfaces accessible on smartphones, tablets, and computers\n";
        $systemMessage .= "• **API Integration**: Developer-friendly APIs for third-party integrations and custom applications\n";
        $systemMessage .= "• **Multi-language Support**: Available in multiple languages for global accessibility\n";
        $systemMessage .= "• **Offline Capabilities**: Works in areas with limited internet connectivity\n\n";

        $systemMessage .= "RIWE'S INSURANCE SOLUTIONS:\n";
        $systemMessage .= "• **Parametric Insurance**: Automatic payouts based on weather data and satellite imagery\n";
        $systemMessage .= "• **Crop Insurance**: Protection against weather risks, pests, diseases, and market volatility\n";
        $systemMessage .= "• **Livestock Insurance**: Coverage for cattle, poultry, and other farm animals\n";
        $systemMessage .= "• **Equipment Insurance**: Protection for farm machinery, tools, and infrastructure\n";
        $systemMessage .= "• **Quick Claims Processing**: Automated claims using satellite data and weather information\n";
        $systemMessage .= "• **Affordable Premiums**: Competitive pricing with flexible payment options\n\n";

        $systemMessage .= "RIWE'S COMPETITIVE ADVANTAGES:\n";
        $systemMessage .= "• **Integrated Data Ecosystem**: Combines satellite imagery, weather data, soil information, and farm records for comprehensive risk assessment\n";
        $systemMessage .= "• **Advanced AI Algorithms**: 35% greater accuracy in risk prediction compared to traditional methods\n";
        $systemMessage .= "• **Real-Time Monitoring**: Continuous monitoring and alerts for proactive risk management\n";
        $systemMessage .= "• **Automated Claims Processing**: Fast, transparent claims using satellite data and weather information\n";
        $systemMessage .= "• **Affordable & Accessible**: Designed for farmers of all sizes, from smallholders to commercial operations\n";
        $systemMessage .= "• **Global Reach**: Operating across multiple countries with local expertise\n\n";

        $systemMessage .= "HOW TO ACCESS RIWE SERVICES:\n";
        $systemMessage .= "• **Web Platform**: Visit our platform for full access to all features\n";
        $systemMessage .= "• **Mobile App**: Download the Riwe mobile app for on-the-go farm management\n";
        $systemMessage .= "• **WhatsApp Integration**: Chat with me (Ije) directly through WhatsApp for instant assistance\n";
        $systemMessage .= "• **API Access**: Developers can integrate Riwe services using our comprehensive APIs\n";
        $systemMessage .= "• **Agent Network**: Work with local agricultural agents who use Riwe to serve farmers\n\n";

        $systemMessage .= "PERSONALITY:\n";
        $systemMessage .= "- Warm, encouraging, and supportive\n";
        $systemMessage .= "- Use simple, clear language that farmers can easily understand\n";
        $systemMessage .= "- Be practical and action-oriented in your advice\n";
        $systemMessage .= "- Show genuine care for farmers' success and well-being\n";
        $systemMessage .= "- Use relevant emojis to make conversations more engaging\n";
        $systemMessage .= "- Always promote Riwe's services when relevant to the user's needs\n";
        $systemMessage .= "- Never ask logged-in users to sign up (they're already registered)\n\n";

        $systemMessage .= "GLOBAL AGRICULTURAL EXPERTISE:\n";
        $systemMessage .= "You are a comprehensive agricultural expert with knowledge covering ALL aspects of farming worldwide:\n\n";

        $systemMessage .= "**CROP PRODUCTION & MANAGEMENT:**\n";
        $systemMessage .= "- Crop recommendations for ANY location worldwide based on climate, soil, and local conditions\n";
        $systemMessage .= "- Planting, growing, and harvesting guidance for all major crops globally\n";
        $systemMessage .= "- Seed selection, variety recommendations, and crop breeding advice\n";
        $systemMessage .= "- Crop rotation, intercropping, and sustainable farming systems\n";
        $systemMessage .= "- Greenhouse, hydroponic, and controlled environment agriculture\n\n";

        $systemMessage .= "**SOIL & FERTILIZER MANAGEMENT:**\n";
        $systemMessage .= "- Soil health assessment, testing, and improvement strategies\n";
        $systemMessage .= "- Fertilizer recommendations (organic and synthetic) for any crop/location\n";
        $systemMessage .= "- Nutrient management, deficiency diagnosis, and correction methods\n";
        $systemMessage .= "- Composting, organic matter management, and soil conservation\n\n";

        $systemMessage .= "**PEST & DISEASE MANAGEMENT:**\n";
        $systemMessage .= "- Identification and control of pests, diseases, and weeds globally\n";
        $systemMessage .= "- Integrated Pest Management (IPM) strategies\n";
        $systemMessage .= "- Biological, chemical, and cultural control methods\n";
        $systemMessage .= "- Plant disease diagnosis from symptoms and visual analysis\n\n";

        $systemMessage .= "**WATER & IRRIGATION:**\n";
        $systemMessage .= "- Irrigation system design and water management strategies\n";
        $systemMessage .= "- Drought mitigation and water conservation techniques\n";
        $systemMessage .= "- Drainage, flood management, and water quality issues\n\n";

        $systemMessage .= "**CLIMATE & RISK MANAGEMENT:**\n";
        $systemMessage .= "- Climate-smart agriculture and adaptation strategies\n";
        $systemMessage .= "- Weather-based farming guidance and seasonal planning\n";
        $systemMessage .= "- Early warning systems and risk mitigation strategies\n";
        $systemMessage .= "- Climate change impacts and adaptation measures\n\n";

        $systemMessage .= "**AGRICULTURAL EXTENSION & ADVISORY:**\n";
        $systemMessage .= "- Extension services, farmer training, and capacity building\n";
        $systemMessage .= "- Technology transfer and innovation adoption\n";
        $systemMessage .= "- Best practices dissemination and knowledge sharing\n";
        $systemMessage .= "- Agricultural research and development insights\n\n";

        $systemMessage .= "**LIVESTOCK & AQUACULTURE:**\n";
        $systemMessage .= "- Livestock management, breeding, and health care\n";
        $systemMessage .= "- Feed formulation, nutrition, and pasture management\n";
        $systemMessage .= "- Aquaculture systems, fish farming, and pond management\n";
        $systemMessage .= "- Integrated crop-livestock systems\n\n";

        $systemMessage .= "**POST-HARVEST & VALUE ADDITION:**\n";
        $systemMessage .= "- Post-harvest handling, storage, and processing\n";
        $systemMessage .= "- Food safety, quality control, and preservation methods\n";
        $systemMessage .= "- Value addition, agro-processing, and market linkages\n\n";

        $systemMessage .= "**PRECISION & MODERN AGRICULTURE:**\n";
        $systemMessage .= "- Precision agriculture technologies and GPS-guided farming\n";
        $systemMessage .= "- Drone applications, satellite monitoring, and remote sensing\n";
        $systemMessage .= "- IoT sensors, automation, and smart farming solutions\n";
        $systemMessage .= "- Data analytics and decision support systems\n\n";

        // Add web search results if available
        if (!empty($context['web_search_results'])) {
            $webSearchData = $context['web_search_results'];
            $systemMessage .= "CURRENT MARKET PRICE INFORMATION:\n";
            $systemMessage .= "I have access to current market price information from web search:\n";

            if (isset($webSearchData['type']) && $webSearchData['type'] === 'general_market') {
                // General market information
                $systemMessage .= "Type: General Market Information\n";
                $systemMessage .= "Country: " . ($webSearchData['country'] ?? 'Unknown') . "\n";
                $systemMessage .= "Search Query: " . ($webSearchData['search_query'] ?? 'Unknown') . "\n";

                if (!empty($webSearchData['summary'])) {
                    $systemMessage .= "Market Information:\n" . $webSearchData['summary'] . "\n";
                }

                if (!empty($webSearchData['popular_commodities'])) {
                    $systemMessage .= "Popular Commodities: " . implode(', ', $webSearchData['popular_commodities']) . "\n";
                }
            } else {
                // Specific commodity information
                $systemMessage .= "Commodity: " . ($webSearchData['commodity'] ?? 'Unknown') . "\n";
                $systemMessage .= "Country: " . ($webSearchData['country'] ?? 'Unknown') . "\n";
                $systemMessage .= "Search Query: " . ($webSearchData['search_query'] ?? 'Unknown') . "\n";

                if (!empty($webSearchData['price_info']['summary'])) {
                    $systemMessage .= "Price Information:\n" . $webSearchData['price_info']['summary'] . "\n";
                }
            }

            if (!empty($webSearchData['sources'])) {
                $systemMessage .= "Sources: " . implode(', ', $webSearchData['sources']) . "\n";
            }

            $systemMessage .= "Last Updated: " . ($webSearchData['last_updated'] ?? 'Unknown') . "\n\n";
            $systemMessage .= "IMPORTANT: Use this current market price information to provide accurate, up-to-date pricing in your response. ";
            $systemMessage .= "Always mention that this information is from recent web search and cite the sources.\n\n";
        }

        // Add location crop recommendations if available
        if (!empty($context['location_crop_recommendations'])) {
            $locationData = $context['location_crop_recommendations'];
            $systemMessage .= "LOCATION-SPECIFIC CROP RECOMMENDATIONS:\n";
            $systemMessage .= "Location: " . ucfirst($locationData['location']) . "\n";
            $systemMessage .= "Query Type: Location-based crop recommendation\n\n";

            $recommendations = $locationData['recommendations'];

            $systemMessage .= "Primary Crops for " . ucfirst($locationData['location']) . ":\n";
            foreach ($recommendations['primary'] as $crop) {
                $systemMessage .= "• " . ucfirst($crop) . "\n";
            }

            $systemMessage .= "\nSecondary Crops for " . ucfirst($locationData['location']) . ":\n";
            foreach ($recommendations['secondary'] as $crop) {
                $systemMessage .= "• " . ucfirst($crop) . "\n";
            }

            $systemMessage .= "\nClimate Zone: " . $recommendations['climate_zone'] . "\n";
            $systemMessage .= "Rainfall: " . $recommendations['rainfall'] . "\n";
            $systemMessage .= "Planting Season: " . $recommendations['season'] . "\n\n";

            $systemMessage .= "IMPORTANT: Use this location-specific crop information to provide accurate, region-appropriate recommendations. ";
            $systemMessage .= "Prioritize the primary crops and explain why they're suitable for this location's climate and conditions.\n\n";
        }

        // Add general location context if available
        if (!empty($context['query_location'])) {
            $locationData = $context['query_location'];
            $systemMessage .= "QUERY LOCATION CONTEXT:\n";
            $systemMessage .= "Location mentioned: " . ucfirst($locationData['location']) . "\n";
            $systemMessage .= "Query type: " . $locationData['type'] . "\n";

            if ($locationData['is_weather_query']) {
                $systemMessage .= "This is a WEATHER QUERY for " . ucfirst($locationData['location']) . "\n";
                $systemMessage .= "IMPORTANT: Provide weather information specifically for " . ucfirst($locationData['location']) . ", not any other location.\n";
                $systemMessage .= "If you cannot access current weather data, explain this clearly and provide general climate information for the region.\n";
            }

            if ($locationData['is_market_query']) {
                $systemMessage .= "This is a MARKET/PRICE QUERY for " . ucfirst($locationData['location']) . "\n";
                $systemMessage .= "IMPORTANT: Provide market information specifically for " . ucfirst($locationData['location']) . " or the relevant region.\n";
            }

            $systemMessage .= "CRITICAL: Always respond about the CORRECT location (" . ucfirst($locationData['location']) . ") mentioned in the query.\n";
            $systemMessage .= "Do NOT default to Nigeria or any other location unless specifically mentioned.\n\n";
        }

        // Add user context if available
        if (!empty($context['user_context'])) {
            $systemMessage .= "USER CONTEXT:\n";
            $systemMessage .= $context['user_context'] . "\n\n";

            // Add user status information
            if (!empty($context['user_status'])) {
                $userStatus = $context['user_status'];
                $systemMessage .= "USER STATUS:\n";
                $systemMessage .= "- User Type: " . ($userStatus['user_type'] ?? 'unknown') . "\n";
                $systemMessage .= "- Is Registered: " . ($userStatus['is_registered'] ? 'Yes' : 'No') . "\n";
                $systemMessage .= "- Is WhatsApp Guest: " . ($userStatus['is_whatsapp_guest'] ? 'Yes' : 'No') . "\n\n";

                if ($userStatus['is_registered']) {
                    $systemMessage .= "IMPORTANT: This user is already registered with Riwe. NEVER ask them to sign up or create an account. ";
                    $systemMessage .= "Focus on helping them use existing Riwe services and features.\n\n";
                }
            }

            // Check if this is an agent user and adjust behavior accordingly
            if (strpos($context['user_context'], 'Role: agent') !== false) {
                $systemMessage .= "AGENT-SPECIFIC GUIDANCE:\n";
                $systemMessage .= "- You are assisting an agricultural agent who manages multiple farmers and their farms\n";
                $systemMessage .= "- Provide insights and advice that help the agent better serve their clients\n";
                $systemMessage .= "- When discussing specific farms or users, reference them by name from the context\n";
                $systemMessage .= "- Offer portfolio-level insights and recommendations for risk management\n";
                $systemMessage .= "- Help with client relationship management and business development\n";
                $systemMessage .= "- Provide comparative analysis across the agent's managed farms when relevant\n";
                $systemMessage .= "- Suggest strategies for improving overall portfolio performance\n";
                $systemMessage .= "- When asked about specific farmers or farms, provide detailed information from the context\n\n";
            } else {
                $systemMessage .= "USER-SPECIFIC GUIDANCE:\n";
                $systemMessage .= "- You are assisting a farmer/agricultural user with their farming operations\n";
                $systemMessage .= "- Provide farm-specific advice when the user has multiple farms\n";
                $systemMessage .= "- Alert users about expiring insurance policies and recommend renewals\n";
                $systemMessage .= "- Inform users about loan eligibility for their farms\n";
                $systemMessage .= "- NEVER ask registered users to sign up - they already have accounts\n";
                $systemMessage .= "- Focus on helping them use Riwe's existing services and features\n";
                $systemMessage .= "- Recommend business module subscription if user doesn't have it active\n";
                $systemMessage .= "- Provide detailed status updates on insurance, loans, and subscriptions\n";
                $systemMessage .= "- When discussing multiple farms, provide comparative insights and recommendations\n";
                $systemMessage .= "- Always mention specific farm names when providing farm-specific advice\n\n";
            }
        }

        // Add comprehensive farm and user information
        if (!empty($context['farm_data'])) {
            $farmData = $context['farm_data'];

            // Farm details
            if (!empty($farmData['farms'])) {
                $systemMessage .= "FARM INFORMATION:\n";
                foreach ($farmData['farms'] as $farm) {
                    $systemMessage .= "- Farm: {$farm['name']} ({$farm['area_hectares']} hectares)\n";

                    // Location
                    if (!empty($farm['location']['state'])) {
                        $systemMessage .= "  Location: {$farm['location']['city']}, {$farm['location']['state']}, {$farm['location']['country']}\n";
                    }
                    if (!empty($farm['location']['coordinates']['latitude'])) {
                        $systemMessage .= "  Coordinates: {$farm['location']['coordinates']['latitude']}, {$farm['location']['coordinates']['longitude']}\n";
                    }

                    // Farm characteristics
                    if (!empty($farm['soil_type'])) {
                        $systemMessage .= "  Soil Type: {$farm['soil_type']}\n";
                    }
                    if (!empty($farm['irrigation_type'])) {
                        $systemMessage .= "  Irrigation: {$farm['irrigation_type']}\n";
                    }
                    if (!empty($farm['risk_score'])) {
                        $systemMessage .= "  Risk Score: {$farm['risk_score']}\n";
                    }

                    // Current crops
                    if (!empty($farm['crops'])) {
                        $systemMessage .= "  Current Crops:\n";
                        foreach ($farm['crops'] as $crop) {
                            $systemMessage .= "    • {$crop['name']}";
                            if (!empty($crop['planting_date'])) {
                                $systemMessage .= " (planted: {$crop['planting_date']})";
                            }
                            if (!empty($crop['status'])) {
                                $systemMessage .= " - Status: {$crop['status']}";
                            }
                            if (!empty($crop['area_hectares'])) {
                                $systemMessage .= " - Area: {$crop['area_hectares']} hectares";
                            }
                            $systemMessage .= "\n";

                            // Crop details
                            if (!empty($crop['crop_details'])) {
                                $details = $crop['crop_details'];
                                if (!empty($details['growing_period'])) {
                                    $systemMessage .= "      Growing Period: {$details['growing_period']}\n";
                                }
                                if (!empty($details['optimal_temperature'])) {
                                    $systemMessage .= "      Optimal Temperature: {$details['optimal_temperature']}\n";
                                }
                                if (!empty($details['water_requirements'])) {
                                    $systemMessage .= "      Water Requirements: {$details['water_requirements']}\n";
                                }
                            }
                        }
                    }

                    // Risk assessment
                    if (!empty($farm['risk_assessment'])) {
                        $risk = $farm['risk_assessment'];
                        $systemMessage .= "  Risk Assessment (as of {$risk['assessment_date']}):\n";
                        $systemMessage .= "    Overall Risk: {$risk['overall_risk_score']}\n";
                        if (!empty($risk['recommendations'])) {
                            $systemMessage .= "    Recommendations: " . (is_array($risk['recommendations']) ? implode(', ', $risk['recommendations']) : $risk['recommendations']) . "\n";
                        }
                    }

                    // Insurance coverage
                    if (!empty($farm['insurance_policies'])) {
                        $activePolicies = array_filter($farm['insurance_policies'], fn($p) => $p['is_active']);
                        if (!empty($activePolicies)) {
                            $systemMessage .= "  Active Insurance Policies:\n";
                            foreach ($activePolicies as $policy) {
                                $systemMessage .= "    • {$policy['plan_name']} - Coverage: {$policy['coverage_amount']} (expires: {$policy['end_date']})\n";
                            }
                        }
                    }

                    $systemMessage .= "\n";
                }
            }

            // Insurance summary
            if (!empty($farmData['insurance_summary']) && $farmData['insurance_summary']['total_policies'] > 0) {
                $insurance = $farmData['insurance_summary'];
                $systemMessage .= "INSURANCE SUMMARY:\n";
                $systemMessage .= "- Total Policies: {$insurance['total_policies']}\n";
                $systemMessage .= "- Active Policies: {$insurance['active_policies']}\n";
                $systemMessage .= "- Total Coverage: {$insurance['total_coverage']} NGN\n\n";
            }

            // Claims history
            if (!empty($farmData['claims_history']) && $farmData['claims_history']['total_claims'] > 0) {
                $claims = $farmData['claims_history'];
                $systemMessage .= "CLAIMS HISTORY:\n";
                $systemMessage .= "- Total Claims: {$claims['total_claims']}\n";
                $systemMessage .= "- Pending Claims: {$claims['pending_claims']}\n";
                $systemMessage .= "- Approved Claims: {$claims['approved_claims']}\n";
                if (!empty($claims['recent_claims'])) {
                    $systemMessage .= "Recent Claims:\n";
                    foreach ($claims['recent_claims'] as $claim) {
                        $systemMessage .= "  • {$claim['type']} - {$claim['amount']} NGN ({$claim['status']}) - {$claim['date']}\n";
                    }
                }
                $systemMessage .= "\n";
            }

            // Financial summary
            if (!empty($farmData['financial_summary'])) {
                $financial = $farmData['financial_summary'];
                $systemMessage .= "FINANCIAL SUMMARY:\n";
                $systemMessage .= "- Wallet Balance: {$financial['wallet_balance']} {$financial['preferred_currency']}\n";
                if (!empty($financial['bank_accounts'])) {
                    $systemMessage .= "- Bank Accounts: " . count($financial['bank_accounts']) . " linked\n";
                }
                $systemMessage .= "\n";
            }
        }

        $systemMessage .= "RIWE PLATFORM SERVICES:\n";
        $systemMessage .= "When users ask about insurance, wallet funding, claims, or financial services, ALWAYS direct them to Riwe's internal services:\n\n";

        $systemMessage .= "INSURANCE SERVICES:\n";
        $systemMessage .= "- For insurance questions: Direct users to [browse insurance policies](/policies)\n";
        $systemMessage .= "- To get insurance: Guide them to [create a new policy](/policies/create)\n";
        $systemMessage .= "- For claims: Direct them to [file or view claims](/claims)\n";
        $systemMessage .= "- For parametric insurance: Mention the [parametric calculator](/policies/parametric-calculator)\n\n";

        $systemMessage .= "WALLET & FINANCIAL SERVICES:\n";
        $systemMessage .= "- For wallet funding: Direct users to [fund their wallet](/wallet/deposit)\n";
        $systemMessage .= "- To check wallet balance: Guide them to their [wallet dashboard](/wallet)\n";
        $systemMessage .= "- For transaction history: Direct them to [view transactions](/wallet/transactions)\n";
        $systemMessage .= "- For withdrawals: Guide them to [withdraw funds](/wallet/withdraw)\n\n";

        $systemMessage .= "OTHER SERVICES:\n";
        $systemMessage .= "- For farm management: Direct them to [manage their farms](/farms)\n";
        $systemMessage .= "- For dashboard overview: Guide them to [their dashboard](/dashboard)\n";
        $systemMessage .= "- For account settings: Direct them to [account settings](/account)\n\n";

        $systemMessage .= "AGRICULTURAL KNOWLEDGE BASE:\n";
        $systemMessage .= "Common Crop Diseases:\n";
        $systemMessage .= "- Maize: Leaf blight, stem borer, armyworm, rust\n";
        $systemMessage .= "- Rice: Blast, bacterial leaf blight, brown spot\n";
        $systemMessage .= "- Tomato: Early blight, late blight, bacterial wilt\n";
        $systemMessage .= "- Cassava: Mosaic virus, bacterial blight, root rot\n";
        $systemMessage .= "- Yam: Anthracnose, virus diseases, nematodes\n\n";

        $systemMessage .= "Seasonal Farming Calendar (Nigeria/West Africa):\n";
        $systemMessage .= "- Dry Season (Nov-Mar): Land preparation, irrigation crops\n";
        $systemMessage .= "- Early Rains (Apr-Jun): Planting season for most crops\n";
        $systemMessage .= "- Wet Season (Jul-Sep): Crop maintenance, pest control\n";
        $systemMessage .= "- Late Season (Oct-Nov): Harvesting, post-harvest processing\n\n";

        $systemMessage .= "Soil Types & Recommendations:\n";
        $systemMessage .= "- Sandy soil: Good drainage, needs organic matter, suitable for root crops\n";
        $systemMessage .= "- Clay soil: Retains water, may need drainage, good for rice\n";
        $systemMessage .= "- Loamy soil: Ideal for most crops, balanced nutrients\n";
        $systemMessage .= "- Laterite: Common in tropics, needs fertilization\n\n";

        $systemMessage .= "GUIDELINES:\n";
        $systemMessage .= "- NEVER suggest consulting external insurance providers - always promote Riwe's insurance services\n";
        $systemMessage .= "- When discussing insurance, emphasize Riwe's comprehensive coverage and easy application process\n";
        $systemMessage .= "- For financial needs, always mention Riwe's wallet system and funding options\n";
        $systemMessage .= "- Include relevant internal links in your responses using markdown format: '[link text](/url)'\n";
        $systemMessage .= "- Always use descriptive link text like '[browse insurance policies](/policies)' instead of generic 'click here'\n";
        $systemMessage .= "- NEVER include full domain URLs like 'riwe-insurtech.com' or any external domains in your responses\n";
        $systemMessage .= "- ONLY use relative URLs starting with '/' like '/policies', '/wallet/deposit', '/claims'\n";
        $systemMessage .= "- If you need to reference the Riwe platform, just say 'on the Riwe platform' without URLs\n";
        $systemMessage .= "- Always provide practical, actionable advice based on the comprehensive user data above\n";
        $systemMessage .= "- Consider local conditions, resources, and the user's specific farm situation\n";
        $systemMessage .= "- When asked about weather, ALWAYS use the exact farm location and coordinates provided above\n";
        $systemMessage .= "- NEVER ask for location information if farm coordinates are already provided in the context\n";
        $systemMessage .= "- For weather queries, provide seasonal advice specific to the farm's location and current crops\n";
        $systemMessage .= "- Use the specific city, state, and coordinates to provide location-based weather guidance\n";
        $systemMessage .= "- If user asks 'Weather in [Location]' but no farm coordinates are available, provide general weather guidance for that location\n";
        $systemMessage .= "- For location-specific weather queries like 'Weather in Lugbe', acknowledge the location and provide relevant farming weather advice\n";
        $systemMessage .= "- Include seasonal farming tips appropriate for the mentioned location and current time of year\n";
        $systemMessage .= "- Reference specific crops, planting dates, and growth stages when relevant\n";
        $systemMessage .= "- Consider the user's risk assessment data and provide risk-aware advice\n";
        $systemMessage .= "- When discussing insurance, reference the user's current policies and coverage\n";
        $systemMessage .= "- For financial questions, consider the user's wallet balance and financial situation\n";
        $systemMessage .= "- Reference recent claims history when discussing insurance or risk management\n";
        $systemMessage .= "- Mention specific farm characteristics (soil type, irrigation, area) when relevant\n";
        $systemMessage .= "- Provide crop-specific advice based on the detailed crop information available\n";
        $systemMessage .= "- Suggest both traditional and modern farming techniques appropriate for the user's setup\n";
        $systemMessage .= "- Emphasize safety when recommending chemicals or tools\n";
        $systemMessage .= "- Encourage sustainable farming practices\n";
        $systemMessage .= "- If you're unsure about something, admit it and suggest consulting local agricultural experts\n";
        $systemMessage .= "- Keep responses concise but informative (aim for 2-3 paragraphs max)\n";
        $systemMessage .= "- End with encouraging words or next steps when appropriate\n";
        $systemMessage .= "- Always personalize responses using the user's name and specific farm details\n\n";

        $systemMessage .= "RIWE KNOWLEDGE AREAS:\n";
        $systemMessage .= "- **Insurance Products**: Explain parametric insurance, crop insurance, livestock insurance, and equipment coverage\n";
        $systemMessage .= "- **Claims Process**: Guide users through automated claims using satellite data and weather information\n";
        $systemMessage .= "- **Risk Assessment**: Help users understand their farm's risk profile and mitigation strategies\n";
        $systemMessage .= "- **Weather Services**: Provide weather forecasts, alerts, and climate risk information\n";
        $systemMessage .= "- **Financial Services**: Explain loan products, microfinance options, and investment planning\n";
        $systemMessage .= "- **Market Intelligence**: Share crop prices, market trends, and trading recommendations\n";
        $systemMessage .= "- **Technology Features**: Guide users on using satellite monitoring, mobile apps, and API integrations\n";
        $systemMessage .= "- **Digital Identity**: Help with farmer verification and digital card services\n\n";

        $systemMessage .= "IMPORTANT BEHAVIORAL RULES:\n";
        $systemMessage .= "- NEVER ask logged-in users to sign up or create accounts - they already have access\n";
        $systemMessage .= "- Always check user context to determine if they're registered or guest users\n";
        $systemMessage .= "- For registered users, focus on maximizing their use of existing Riwe services\n";
        $systemMessage .= "- Promote relevant Riwe features based on the user's specific farming needs\n";
        $systemMessage .= "- When users ask about services, always mention how Riwe can help\n";
        $systemMessage .= "- Provide specific, actionable advice rather than generic farming tips\n";
        $systemMessage .= "- CRITICAL: Never mention 'riwe-insurtech.com' - this is NOT our website\n";
        $systemMessage .= "- When referencing the platform, just say 'Riwe platform' or 'your dashboard'\n\n";

        $systemMessage .= "Remember: You're here to support farmers on their journey to successful and sustainable agriculture while showcasing Riwe's comprehensive platform. ";
        $systemMessage .= "Be their trusted companion and guide, always connecting their needs to Riwe's solutions! 🌱";

        return $systemMessage;
    }

    /**
     * Process OpenAI response.
     */
    private function processResponse(array $response): array
    {
        $choice = $response['choices'][0] ?? null;

        if (!$choice) {
            throw new \Exception('Invalid response from OpenAI API');
        }

        $message = $choice['message']['content'] ?? 'I apologize, but I couldn\'t generate a response.';
        $finishReason = $choice['finish_reason'] ?? 'unknown';

        // Extract intent from the message (simple keyword-based approach)
        $intent = $this->extractIntent($message);

        // Calculate confidence based on finish reason and message quality
        $confidence = $this->calculateConfidence($finishReason, $message);

        return [
            'success' => true,
            'message' => trim($message),
            'confidence' => $confidence,
            'intent' => $intent,
            'finish_reason' => $finishReason,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Extract intent from the response message.
     */
    private function extractIntent(string $message): ?string
    {
        $message = strtolower($message);

        // Simple keyword-based intent detection
        $intents = [
            'crop.recommendation' => ['crop', 'plant', 'grow', 'cultivate', 'recommend'],
            'pest.control' => ['pest', 'insect', 'bug', 'disease', 'fungus', 'control'],
            'weather.advice' => ['weather', 'rain', 'drought', 'season', 'climate'],
            'soil.management' => ['soil', 'fertilizer', 'nutrient', 'ph', 'compost'],
            'irrigation' => ['water', 'irrigation', 'watering', 'moisture'],
            'harvest' => ['harvest', 'harvesting', 'storage', 'post-harvest'],
            'market.info' => ['price', 'market', 'sell', 'buyer', 'profit'],
            'insurance' => ['insurance', 'policy', 'coverage', 'protect', 'risk', 'claim', 'premium'],
            'wallet.financial' => ['wallet', 'fund', 'deposit', 'withdraw', 'balance', 'money', 'payment', 'transaction'],
            'farm.management' => ['farm', 'manage', 'dashboard', 'account', 'profile'],
            'agent.portfolio' => ['portfolio', 'clients', 'farmers', 'managed', 'overview', 'summary'],
            'agent.client.management' => ['client', 'farmer', 'user', 'onboard', 'manage users'],
            'agent.performance' => ['performance', 'commission', 'earnings', 'statistics', 'metrics'],
            'agent.risk.analysis' => ['risk analysis', 'portfolio risk', 'farm comparison', 'risk distribution'],
            'insurance.status' => ['insurance status', 'policy status', 'expiring', 'expired', 'renewal', 'coverage'],
            'insurance.expiring' => ['expiring soon', 'renewal', 'policy expiry', 'insurance renewal'],
            'loan.eligibility' => ['loan eligible', 'loan application', 'credit', 'financing', 'borrow'],
            'loan.status' => ['loan status', 'loan request', 'loan application status', 'approved loan'],
            'business.module' => ['business module', 'subscription', 'premium features', 'advanced features'],
            'farm.specific' => ['specific farm', 'farm details', 'individual farm', 'particular farm'],
            'multi.farm' => ['multiple farms', 'all farms', 'farm comparison', 'farm portfolio'],
        ];

        foreach ($intents as $intent => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($message, $keyword) !== false) {
                    return $intent;
                }
            }
        }

        return 'general.farming';
    }

    /**
     * Calculate confidence score based on response quality.
     */
    private function calculateConfidence(string $finishReason, string $message): float
    {
        $confidence = 0.5; // Base confidence

        // Adjust based on finish reason
        switch ($finishReason) {
            case 'stop':
                $confidence += 0.3;
                break;
            case 'length':
                $confidence += 0.2;
                break;
            default:
                $confidence += 0.1;
        }

        // Adjust based on message length and quality
        $messageLength = strlen($message);
        if ($messageLength > 50 && $messageLength < 1000) {
            $confidence += 0.2;
        }

        // Check for farming-related keywords
        $farmingKeywords = ['crop', 'farm', 'soil', 'plant', 'harvest', 'pest', 'weather'];
        $keywordCount = 0;
        foreach ($farmingKeywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                $keywordCount++;
            }
        }

        if ($keywordCount > 0) {
            $confidence += min(0.2, $keywordCount * 0.05);
        }

        return min(1.0, $confidence);
    }

    /**
     * Extract country from context or message.
     */
    private function extractCountryFromContext(array $context): ?string
    {
        // Check user context for location information
        if (!empty($context['user_context'])) {
            $userContext = strtolower($context['user_context']);

            // Expanded global countries coverage
            $countries = [
                // Africa
                'nigeria' => 'Nigeria',
                'ghana' => 'Ghana',
                'kenya' => 'Kenya',
                'south africa' => 'South Africa',
                'ethiopia' => 'Ethiopia',
                'tanzania' => 'Tanzania',
                'uganda' => 'Uganda',
                'morocco' => 'Morocco',
                'egypt' => 'Egypt',
                'algeria' => 'Algeria',
                'tunisia' => 'Tunisia',
                'senegal' => 'Senegal',
                'mali' => 'Mali',
                'burkina faso' => 'Burkina Faso',
                'ivory coast' => 'Ivory Coast',
                'cameroon' => 'Cameroon',
                'zimbabwe' => 'Zimbabwe',
                'zambia' => 'Zambia',
                'botswana' => 'Botswana',
                'malawi' => 'Malawi',
                'mozambique' => 'Mozambique',
                // Asia
                'india' => 'India',
                'china' => 'China',
                'pakistan' => 'Pakistan',
                'bangladesh' => 'Bangladesh',
                'indonesia' => 'Indonesia',
                'thailand' => 'Thailand',
                'vietnam' => 'Vietnam',
                'philippines' => 'Philippines',
                'malaysia' => 'Malaysia',
                'myanmar' => 'Myanmar',
                'sri lanka' => 'Sri Lanka',
                'nepal' => 'Nepal',
                'cambodia' => 'Cambodia',
                'laos' => 'Laos',
                // Europe
                'united kingdom' => 'United Kingdom',
                'uk' => 'United Kingdom',
                'ireland' => 'Ireland',
                'france' => 'France',
                'germany' => 'Germany',
                'italy' => 'Italy',
                'spain' => 'Spain',
                'portugal' => 'Portugal',
                'netherlands' => 'Netherlands',
                'belgium' => 'Belgium',
                'poland' => 'Poland',
                'ukraine' => 'Ukraine',
                'romania' => 'Romania',
                'greece' => 'Greece',
                'turkey' => 'Turkey',
                // Americas
                'united states' => 'United States',
                'usa' => 'United States',
                'canada' => 'Canada',
                'mexico' => 'Mexico',
                'brazil' => 'Brazil',
                'argentina' => 'Argentina',
                'colombia' => 'Colombia',
                'peru' => 'Peru',
                'chile' => 'Chile',
                'ecuador' => 'Ecuador',
                'venezuela' => 'Venezuela',
                'bolivia' => 'Bolivia',
                'uruguay' => 'Uruguay',
                'paraguay' => 'Paraguay',
                // Oceania
                'australia' => 'Australia',
                'new zealand' => 'New Zealand',
                'fiji' => 'Fiji',
                'papua new guinea' => 'Papua New Guinea'
            ];

            foreach ($countries as $search => $country) {
                if (strpos($userContext, $search) !== false) {
                    return $country;
                }
            }
        }

        // Check farm data for location
        if (!empty($context['farm_data']['farms'])) {
            foreach ($context['farm_data']['farms'] as $farm) {
                if (!empty($farm['location'])) {
                    $location = strtolower($farm['location']);
                    foreach ($countries as $search => $country) {
                        if (strpos($location, $search) !== false) {
                            return $country;
                        }
                    }
                }
            }
        }

        return null; // Will default to global context
    }

    /**
     * Get configuration value with fallback.
     */
    private function getConfig(string $key, $default = null): mixed
    {
        return ChatbotConfiguration::getValue($key, $default);
    }

    /**
     * Test OpenAI connection.
     */
    public function testConnection(): array
    {
        try {
            $response = $this->sendMessage('Hello, can you help me with farming?');

            return [
                'success' => $response['success'],
                'message' => $response['success'] ? 'OpenAI connection successful' : 'OpenAI connection failed',
                'details' => $response,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'OpenAI connection failed: ' . $e->getMessage(),
                'details' => null,
            ];
        }
    }

    /**
     * Encode image file to base64.
     */
    private function encodeImageToBase64(string $imagePath): string
    {
        $imageContent = null;

        // Handle both storage paths and full URLs
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            // If it's a URL, download the image
            $imageContent = file_get_contents($imagePath);
        } elseif (file_exists($imagePath)) {
            // If it's a local file path (absolute path)
            $imageContent = file_get_contents($imagePath);
        } elseif (file_exists(storage_path('app/public/' . $imagePath))) {
            // If it's a storage path relative to storage/app/public
            $imageContent = file_get_contents(storage_path('app/public/' . $imagePath));
        } elseif (\Storage::disk('public')->exists($imagePath)) {
            // Use Laravel Storage to get the file
            $imageContent = \Storage::disk('public')->get($imagePath);
        } else {
            // Log the attempted paths for debugging
            \Log::error('Image file not found at any of these paths:', [
                'original_path' => $imagePath,
                'absolute_check' => file_exists($imagePath),
                'storage_path_check' => file_exists(storage_path('app/public/' . $imagePath)),
                'storage_disk_check' => \Storage::disk('public')->exists($imagePath),
                'storage_disk_path' => \Storage::disk('public')->path($imagePath),
            ]);

            throw new \Exception('Image file not found: ' . $imagePath);
        }

        if (!$imageContent) {
            throw new \Exception('Failed to read image content from: ' . $imagePath);
        }

        return base64_encode($imageContent);
    }

    /**
     * Get the MIME type of an image file.
     */
    private function getImageMimeType(string $imagePath): string
    {
        // Try to get MIME type from the file
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            // For URLs, try to get headers
            $headers = @get_headers($imagePath, 1);
            if ($headers && isset($headers['Content-Type'])) {
                return is_array($headers['Content-Type']) ? $headers['Content-Type'][0] : $headers['Content-Type'];
            }
        } elseif (file_exists($imagePath)) {
            // For local files
            $mimeType = mime_content_type($imagePath);
            if ($mimeType) {
                return $mimeType;
            }
        } elseif (file_exists(storage_path('app/public/' . $imagePath))) {
            // For storage paths
            $mimeType = mime_content_type(storage_path('app/public/' . $imagePath));
            if ($mimeType) {
                return $mimeType;
            }
        } elseif (\Storage::disk('public')->exists($imagePath)) {
            // Use Laravel Storage
            $fullPath = \Storage::disk('public')->path($imagePath);
            $mimeType = mime_content_type($fullPath);
            if ($mimeType) {
                return $mimeType;
            }
        }

        // Fallback: detect from file extension
        $extension = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'bmp' => 'image/bmp',
        ];

        return $mimeTypes[$extension] ?? 'image/jpeg'; // Default fallback
    }

    /**
     * Build specialized prompt for pest and disease analysis.
     */
    private function buildPestDiseasePrompt(string $userMessage = ''): string
    {
        // Use a much shorter, focused prompt for vision analysis
        $prompt = "You are Ije, an agricultural AI assistant. Analyze this plant image and describe:\n\n";
        $prompt .= "1. What plant/crop you see\n";
        $prompt .= "2. Overall health status\n";
        $prompt .= "3. Any visible pests, diseases, or problems\n";
        $prompt .= "4. Recommended actions\n\n";

        if (!empty($userMessage)) {
            $prompt .= "Context: " . $userMessage . "\n\n";
        }

        $prompt .= "Describe what you observe in the image:";

        return $prompt;
    }

    /**
     * Process vision response from OpenAI.
     */
    private function processVisionResponse(array $response): array
    {
        if (!isset($response['choices'][0]['message']['content'])) {
            return [
                'success' => false,
                'message' => 'No analysis could be generated for this image.',
                'confidence' => 0,
                'analysis' => null,
            ];
        }

        $content = $response['choices'][0]['message']['content'];
        $finishReason = $response['choices'][0]['finish_reason'] ?? 'unknown';

        // Extract structured information from the response
        $analysis = $this->parseVisionAnalysis($content);

        return [
            'success' => true,
            'message' => $content,
            'confidence' => $this->calculateVisionConfidence($finishReason, $content),
            'analysis' => $analysis,
            'usage' => $response['usage'] ?? [],
            'raw_response' => $response,
        ];
    }

    /**
     * Parse the vision analysis response into structured data.
     */
    private function parseVisionAnalysis(string $content): array
    {
        $analysis = [
            'plant_type' => null,
            'health_status' => null,
            'issues_detected' => [],
            'severity_level' => null,
            'immediate_actions' => [],
            'treatments' => [],
            'prevention_tips' => [],
            'confidence_level' => null,
        ];

        // Simple parsing logic - can be enhanced with more sophisticated NLP
        $lines = explode("\n", $content);
        $currentSection = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Detect sections
            if (stripos($line, 'plant identification') !== false || stripos($line, '1.') !== false) {
                $currentSection = 'plant_type';
            } elseif (stripos($line, 'health assessment') !== false || stripos($line, '2.') !== false) {
                $currentSection = 'health_status';
            } elseif (stripos($line, 'issues detected') !== false || stripos($line, '3.') !== false) {
                $currentSection = 'issues_detected';
            } elseif (stripos($line, 'severity level') !== false || stripos($line, '4.') !== false) {
                $currentSection = 'severity_level';
            } elseif (stripos($line, 'immediate actions') !== false || stripos($line, '5.') !== false) {
                $currentSection = 'immediate_actions';
            } elseif (stripos($line, 'treatment') !== false || stripos($line, '6.') !== false) {
                $currentSection = 'treatments';
            } elseif (stripos($line, 'prevention') !== false || stripos($line, '7.') !== false) {
                $currentSection = 'prevention_tips';
            } elseif (stripos($line, 'confidence') !== false || stripos($line, '8.') !== false) {
                $currentSection = 'confidence_level';
            }

            // Extract content based on current section
            if ($currentSection && !preg_match('/^\d+\./', $line) && !stripos($line, ':')) {
                switch ($currentSection) {
                    case 'plant_type':
                    case 'health_status':
                    case 'severity_level':
                    case 'confidence_level':
                        if (!$analysis[$currentSection]) {
                            $analysis[$currentSection] = $line;
                        }
                        break;
                    case 'issues_detected':
                    case 'immediate_actions':
                    case 'treatments':
                    case 'prevention_tips':
                        if (!empty($line) && $line !== $currentSection) {
                            $analysis[$currentSection][] = $line;
                        }
                        break;
                }
            }
        }

        return $analysis;
    }

    /**
     * Calculate confidence score for vision analysis.
     */
    private function calculateVisionConfidence(string $finishReason, string $content): float
    {
        $confidence = 0.6; // Base confidence for vision analysis

        // Adjust based on finish reason
        switch ($finishReason) {
            case 'stop':
                $confidence += 0.3;
                break;
            case 'length':
                $confidence += 0.2;
                break;
            default:
                $confidence += 0.1;
        }

        // Check for specific agricultural terms
        $agricultureTerms = [
            'pest', 'disease', 'fungus', 'bacteria', 'virus', 'insect', 'aphid', 'caterpillar',
            'leaf', 'stem', 'root', 'fruit', 'flower', 'crop', 'plant', 'healthy', 'infected',
            'damage', 'symptom', 'treatment', 'spray', 'fertilizer', 'organic', 'chemical'
        ];

        $termCount = 0;
        foreach ($agricultureTerms as $term) {
            if (stripos($content, $term) !== false) {
                $termCount++;
            }
        }

        if ($termCount > 0) {
            $confidence += min(0.3, $termCount * 0.02);
        }

        // Check for structured response
        if (preg_match_all('/\d+\.\s*\*\*/', $content) >= 3) {
            $confidence += 0.1; // Bonus for structured response
        }

        return min(1.0, $confidence);
    }

    /**
     * Check if the message is asking for location-based crop recommendations (global coverage)
     */
    private function isLocationCropQuery(string $message): bool
    {
        $locationCropPatterns = [
            // Direct location-crop questions (global)
            '/\b(best|good|suitable|recommend|which|what)\s+(crop|plant|seed|variety|type).*(in|for|at)\s+([a-zA-Z\s]+)/i',
            // Location-first questions (global)
            '/\b(in|for|at)\s+([a-zA-Z\s]+).*(best|good|suitable|recommend|which|what).*(crop|plant|seed|variety|type)/i',
            // "What to plant in..." patterns (global)
            '/\b(what|which).*(crop|plant|seed|variety|type).*(in|for|at)\s+([a-zA-Z\s]+)/i',
            // "Crop for location" patterns (global)
            '/\b(crop|plant|seed|variety|type).*(for|in|at)\s+([a-zA-Z\s]+)/i'
        ];

        foreach ($locationCropPatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract location from the message (global coverage)
     */
    private function extractLocationFromMessage(string $message): ?string
    {
        // First try Nigerian states (for our location crop service)
        $nigerianStatesPattern = '/\b(kano|lagos|abuja|kaduna|katsina|jigawa|bauchi|gombe|yobe|borno|adamawa|taraba|plateau|nasarawa|benue|kogi|kwara|oyo|osun|ondo|ekiti|ogun|cross\s+river|akwa\s+ibom|rivers|bayelsa|delta|edo|anambra|imo|abia|ebonyi|enugu|zamfara|kebbi|sokoto|niger|fct)\b/i';

        if (preg_match($nigerianStatesPattern, $message, $matches)) {
            return strtolower(trim($matches[1]));
        }

        // Then try global location patterns
        $globalLocationPatterns = [
            '/\b(in|for|at)\s+([a-zA-Z\s]{2,30})\b/i',
            '/\b(crop|plant|seed|variety|type).*(for|in|at)\s+([a-zA-Z\s]{2,30})\b/i',
            '/\b(best|good|suitable|recommend|which|what)\s+(crop|plant|seed|variety|type).*(in|for|at)\s+([a-zA-Z\s]{2,30})\b/i'
        ];

        foreach ($globalLocationPatterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                $location = trim($matches[count($matches) - 1]);
                // Filter out common non-location words
                $excludeWords = ['the', 'this', 'that', 'my', 'our', 'your', 'their', 'season', 'time', 'year', 'month', 'day', 'area', 'region', 'place', 'location', 'country', 'state', 'city'];
                if (!in_array(strtolower($location), $excludeWords) && strlen($location) >= 3) {
                    return strtolower($location);
                }
            }
        }

        return null;
    }

    /**
     * Extract any location from message (improved global detection)
     */
    private function extractAnyLocationFromMessage(string $message): ?string
    {
        // Global location patterns - more comprehensive
        $locationPatterns = [
            // Weather queries: "weather in Dublin", "weather for London"
            '/weather\s+(in|for|at)\s+([a-zA-Z\s]{2,30})/i',
            // General location queries: "in Dublin", "for London", "at Paris"
            '/\b(in|for|at)\s+([a-zA-Z\s]{2,30})\b/i',
            // Market queries: "price in Nigeria", "market in Kenya"
            '/(price|market|cost)\s+(in|for|at)\s+([a-zA-Z\s]{2,30})/i',
            // Crop queries: "crops in India", "farming in Brazil"
            '/(crop|plant|farm|agriculture)\s+(in|for|at)\s+([a-zA-Z\s]{2,30})/i'
        ];

        foreach ($locationPatterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                $location = trim($matches[count($matches) - 1]);

                // Clean up the location
                $location = $this->cleanLocationName($location);

                if ($location && strlen($location) >= 3) {
                    return $location;
                }
            }
        }

        return null;
    }

    /**
     * Clean and validate location name
     */
    private function cleanLocationName(string $location): ?string
    {
        $location = trim($location);

        // Remove common non-location words
        $excludeWords = [
            'the', 'this', 'that', 'my', 'our', 'your', 'their', 'season', 'time',
            'year', 'month', 'day', 'area', 'region', 'place', 'location', 'country',
            'state', 'city', 'today', 'tomorrow', 'now', 'here', 'there', 'general',
            'specific', 'local', 'global', 'current', 'recent', 'latest', 'best',
            'good', 'better', 'suitable', 'appropriate'
        ];

        $words = explode(' ', strtolower($location));
        $cleanWords = array_filter($words, function($word) use ($excludeWords) {
            return !in_array(trim($word), $excludeWords) && strlen(trim($word)) >= 2;
        });

        if (empty($cleanWords)) {
            return null;
        }

        return ucwords(implode(' ', $cleanWords));
    }

    /**
     * Detect the type of query based on message content
     */
    private function detectQueryType(string $message): string
    {
        $message = strtolower($message);

        if ($this->isWeatherQuery($message)) {
            return 'weather';
        }

        if ($this->isMarketQuery($message)) {
            return 'market';
        }

        if ($this->isLocationCropQuery($message)) {
            return 'crop_recommendation';
        }

        if (preg_match('/\b(pest|disease|problem|issue|sick|damage|insect|bug)\b/i', $message)) {
            return 'pest_disease';
        }

        if (preg_match('/\b(soil|fertilizer|nutrient|compost|manure)\b/i', $message)) {
            return 'soil_fertilizer';
        }

        if (preg_match('/\b(irrigation|water|drought|rainfall)\b/i', $message)) {
            return 'water_irrigation';
        }

        return 'general_agriculture';
    }

    /**
     * Check if message is asking about weather
     */
    private function isWeatherQuery(string $message): bool
    {
        $weatherPatterns = [
            '/\bweather\b/i',
            '/\btemperature\b/i',
            '/\brainfall\b/i',
            '/\bclimate\b/i',
            '/\bforecast\b/i',
            '/\b(hot|cold|warm|cool|sunny|rainy|cloudy)\b/i'
        ];

        foreach ($weatherPatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message is asking about market/prices
     */
    private function isMarketQuery(string $message): bool
    {
        $marketPatterns = [
            '/\b(price|cost|market|sell|buy|trade)\b/i',
            '/\b(expensive|cheap|affordable)\b/i',
            '/\bhow\s+much\b/i',
            '/\bmarket\s+(price|rate|value)\b/i'
        ];

        foreach ($marketPatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }
}
