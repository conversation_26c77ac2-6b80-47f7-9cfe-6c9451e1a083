<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WhatsAppWeatherService
{
    protected $openWeatherApiKey;
    protected $cacheTime = 1800; // 30 minutes

    public function __construct()
    {
        $this->openWeatherApiKey = config('services.openweather.key');
    }

    /**
     * Get weather data for a location mentioned in WhatsApp message
     */
    public function getWeatherForLocation(string $locationName): array
    {
        try {
            // First, get coordinates for the location
            $coordinates = $this->geocodeLocation($locationName);
            
            if (!$coordinates) {
                return [
                    'success' => false,
                    'error' => 'Location not found',
                    'message' => "I couldn't find the location '{$locationName}'. Please check the spelling or try a nearby major city."
                ];
            }

            // Get weather data using coordinates
            $weatherData = $this->getWeatherData($coordinates['lat'], $coordinates['lon']);
            
            if (!$weatherData) {
                return [
                    'success' => false,
                    'error' => 'Weather data unavailable',
                    'message' => "I couldn't get weather data for {$locationName} right now. Please try again later."
                ];
            }

            // Format weather data for WhatsApp
            $formattedWeather = $this->formatWeatherForWhatsApp($weatherData, $locationName);

            return [
                'success' => true,
                'location' => $locationName,
                'coordinates' => $coordinates,
                'weather_data' => $weatherData,
                'formatted_message' => $formattedWeather
            ];

        } catch (\Exception $e) {
            Log::error('WhatsApp Weather Service Error', [
                'location' => $locationName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Service error',
                'message' => "I'm having trouble getting weather data right now. Please try again in a few minutes."
            ];
        }
    }

    /**
     * Geocode location name to coordinates (GLOBAL SUPPORT)
     */
    protected function geocodeLocation(string $locationName): ?array
    {
        $cacheKey = "geocode_global_" . md5(strtolower($locationName));

        return Cache::remember($cacheKey, 86400, function () use ($locationName) { // Cache for 24 hours
            try {
                Log::info('Geocoding location globally', ['location' => $locationName]);

                // Step 1: Try OpenWeatherMap Geocoding API first (GLOBAL - no country restriction)
                $response = Http::get('http://api.openweathermap.org/geo/1.0/direct', [
                    'q' => $locationName, // No country restriction for global support
                    'limit' => 5, // Get multiple results to find best match
                    'appid' => config('services.openweather.key')
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    if (!empty($data)) {
                        // Return the first (most relevant) result
                        $result = [
                            'lat' => $data[0]['lat'],
                            'lon' => $data[0]['lon'],
                            'name' => $data[0]['name'],
                            'country' => $data[0]['country'] ?? 'Unknown',
                            'state' => $data[0]['state'] ?? null
                        ];

                        Log::info('OpenWeatherMap geocoding successful', [
                            'location' => $locationName,
                            'result' => $result
                        ]);

                        return $result;
                    }
                }

                // Step 2: Try with common country variations if no country specified
                $commonCountries = ['US', 'GB', 'CA', 'AU', 'DE', 'FR', 'IN', 'CN', 'JP', 'BR', 'NG', 'KE', 'ZA', 'EG', 'MA'];

                foreach ($commonCountries as $country) {
                    $response = Http::get('http://api.openweathermap.org/geo/1.0/direct', [
                        'q' => $locationName . ',' . $country,
                        'limit' => 1,
                        'appid' => config('services.openweather.key')
                    ]);

                    if ($response->successful()) {
                        $data = $response->json();
                        if (!empty($data)) {
                            $result = [
                                'lat' => $data[0]['lat'],
                                'lon' => $data[0]['lon'],
                                'name' => $data[0]['name'],
                                'country' => $data[0]['country'] ?? $country,
                                'state' => $data[0]['state'] ?? null
                            ];

                            Log::info('OpenWeatherMap geocoding with country successful', [
                                'location' => $locationName,
                                'country' => $country,
                                'result' => $result
                            ]);

                            return $result;
                        }
                    }
                }

                // Step 3: Fallback to Nominatim (OpenStreetMap) - GLOBAL
                $response = Http::get('https://nominatim.openstreetmap.org/search', [
                    'q' => $locationName, // No country restriction
                    'format' => 'json',
                    'limit' => 1,
                    'addressdetails' => 1
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    if (!empty($data)) {
                        $result = [
                            'lat' => (float) $data[0]['lat'],
                            'lon' => (float) $data[0]['lon'],
                            'name' => $data[0]['display_name'],
                            'country' => $data[0]['address']['country_code'] ?? 'Unknown'
                        ];

                        Log::info('Nominatim geocoding successful', [
                            'location' => $locationName,
                            'result' => $result
                        ]);

                        return $result;
                    }
                }

                // Step 4: Global known locations database (major cities worldwide)
                $globalKnownLocations = [
                    // Africa
                    'lagos' => ['lat' => 6.5244, 'lon' => 3.3792, 'name' => 'Lagos, Nigeria', 'country' => 'NG'],
                    'abuja' => ['lat' => 9.0765, 'lon' => 7.3986, 'name' => 'Abuja, Nigeria', 'country' => 'NG'],
                    'nairobi' => ['lat' => -1.2921, 'lon' => 36.8219, 'name' => 'Nairobi, Kenya', 'country' => 'KE'],
                    'cairo' => ['lat' => 30.0444, 'lon' => 31.2357, 'name' => 'Cairo, Egypt', 'country' => 'EG'],
                    'casablanca' => ['lat' => 33.5731, 'lon' => -7.5898, 'name' => 'Casablanca, Morocco', 'country' => 'MA'],
                    'morocco' => ['lat' => 31.7917, 'lon' => -7.0926, 'name' => 'Morocco', 'country' => 'MA'],
                    'accra' => ['lat' => 5.6037, 'lon' => -0.1870, 'name' => 'Accra, Ghana', 'country' => 'GH'],
                    'johannesburg' => ['lat' => -26.2041, 'lon' => 28.0473, 'name' => 'Johannesburg, South Africa', 'country' => 'ZA'],

                    // Europe
                    'london' => ['lat' => 51.5074, 'lon' => -0.1278, 'name' => 'London, UK', 'country' => 'GB'],
                    'dublin' => ['lat' => 53.3498, 'lon' => -6.2603, 'name' => 'Dublin, Ireland', 'country' => 'IE'],
                    'paris' => ['lat' => 48.8566, 'lon' => 2.3522, 'name' => 'Paris, France', 'country' => 'FR'],
                    'berlin' => ['lat' => 52.5200, 'lon' => 13.4050, 'name' => 'Berlin, Germany', 'country' => 'DE'],
                    'madrid' => ['lat' => 40.4168, 'lon' => -3.7038, 'name' => 'Madrid, Spain', 'country' => 'ES'],
                    'rome' => ['lat' => 41.9028, 'lon' => 12.4964, 'name' => 'Rome, Italy', 'country' => 'IT'],

                    // Asia
                    'tokyo' => ['lat' => 35.6762, 'lon' => 139.6503, 'name' => 'Tokyo, Japan', 'country' => 'JP'],
                    'beijing' => ['lat' => 39.9042, 'lon' => 116.4074, 'name' => 'Beijing, China', 'country' => 'CN'],
                    'mumbai' => ['lat' => 19.0760, 'lon' => 72.8777, 'name' => 'Mumbai, India', 'country' => 'IN'],
                    'delhi' => ['lat' => 28.7041, 'lon' => 77.1025, 'name' => 'Delhi, India', 'country' => 'IN'],
                    'bangkok' => ['lat' => 13.7563, 'lon' => 100.5018, 'name' => 'Bangkok, Thailand', 'country' => 'TH'],
                    'singapore' => ['lat' => 1.3521, 'lon' => 103.8198, 'name' => 'Singapore', 'country' => 'SG'],

                    // Americas
                    'new york' => ['lat' => 40.7128, 'lon' => -74.0060, 'name' => 'New York, USA', 'country' => 'US'],
                    'los angeles' => ['lat' => 34.0522, 'lon' => -118.2437, 'name' => 'Los Angeles, USA', 'country' => 'US'],
                    'toronto' => ['lat' => 43.6532, 'lon' => -79.3832, 'name' => 'Toronto, Canada', 'country' => 'CA'],
                    'mexico city' => ['lat' => 19.4326, 'lon' => -99.1332, 'name' => 'Mexico City, Mexico', 'country' => 'MX'],
                    'sao paulo' => ['lat' => -23.5505, 'lon' => -46.6333, 'name' => 'São Paulo, Brazil', 'country' => 'BR'],

                    // Oceania
                    'sydney' => ['lat' => -33.8688, 'lon' => 151.2093, 'name' => 'Sydney, Australia', 'country' => 'AU'],
                    'melbourne' => ['lat' => -37.8136, 'lon' => 144.9631, 'name' => 'Melbourne, Australia', 'country' => 'AU'],

                    // Nigerian cities and districts
                    'lugbe' => ['lat' => 8.9806, 'lon' => 7.3986, 'name' => 'Lugbe, Abuja', 'country' => 'NG'],
                    'garki' => ['lat' => 9.0579, 'lon' => 7.4951, 'name' => 'Garki, Abuja', 'country' => 'NG'],
                    'wuse' => ['lat' => 9.0579, 'lon' => 7.4951, 'name' => 'Wuse, Abuja', 'country' => 'NG'],
                    'maitama' => ['lat' => 9.0579, 'lon' => 7.4951, 'name' => 'Maitama, Abuja', 'country' => 'NG'],
                ];

                $locationKey = strtolower($locationName);
                if (isset($globalKnownLocations[$locationKey])) {
                    $result = [
                        'lat' => $globalKnownLocations[$locationKey]['lat'],
                        'lon' => $globalKnownLocations[$locationKey]['lon'],
                        'name' => $globalKnownLocations[$locationKey]['name'],
                        'country' => $globalKnownLocations[$locationKey]['country']
                    ];

                    Log::info('Known location found', [
                        'location' => $locationName,
                        'result' => $result
                    ]);

                    return $result;
                }

                Log::warning('Location not found in any geocoding service', ['location' => $locationName]);
                return null;

            } catch (\Exception $e) {
                Log::error('Geocoding failed', [
                    'location' => $locationName,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        });
    }

    /**
     * Get weather data from OpenWeatherMap
     */
    protected function getWeatherData(float $lat, float $lon): ?array
    {
        $cacheKey = "weather_" . md5("{$lat}_{$lon}");
        
        return Cache::remember($cacheKey, $this->cacheTime, function () use ($lat, $lon) {
            try {
                // Get current weather and 5-day forecast
                $currentResponse = Http::get('https://api.openweathermap.org/data/2.5/weather', [
                    'lat' => $lat,
                    'lon' => $lon,
                    'appid' => $this->openWeatherApiKey,
                    'units' => 'metric'
                ]);

                $forecastResponse = Http::get('https://api.openweathermap.org/data/2.5/forecast', [
                    'lat' => $lat,
                    'lon' => $lon,
                    'appid' => $this->openWeatherApiKey,
                    'units' => 'metric',
                    'cnt' => 8 // Next 24 hours (3-hour intervals)
                ]);

                if ($currentResponse->successful() && $forecastResponse->successful()) {
                    return [
                        'current' => $currentResponse->json(),
                        'forecast' => $forecastResponse->json()
                    ];
                }

                return null;

            } catch (\Exception $e) {
                Log::error('Weather API failed', [
                    'lat' => $lat,
                    'lon' => $lon,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        });
    }

    /**
     * Format weather data for WhatsApp message
     */
    protected function formatWeatherForWhatsApp(array $weatherData, string $locationName): string
    {
        $current = $weatherData['current'];
        $forecast = $weatherData['forecast'];

        $message = "🌤️ *Weather in {$locationName}* 🌤️\n\n";
        
        // Current weather
        $temp = round($current['main']['temp']);
        $feelsLike = round($current['main']['feels_like']);
        $humidity = $current['main']['humidity'];
        $description = ucfirst($current['weather'][0]['description']);
        $windSpeed = round($current['wind']['speed'] * 3.6, 1); // Convert m/s to km/h
        
        $message .= "📍 *Current Conditions:*\n";
        $message .= "🌡️ Temperature: {$temp}°C (feels like {$feelsLike}°C)\n";
        $message .= "☁️ Conditions: {$description}\n";
        $message .= "💧 Humidity: {$humidity}%\n";
        $message .= "💨 Wind: {$windSpeed} km/h\n\n";

        // Today's forecast
        $message .= "📅 *Today's Forecast:*\n";
        $todayForecasts = array_slice($forecast['list'], 0, 4); // Next 12 hours
        
        foreach ($todayForecasts as $item) {
            $time = date('H:i', $item['dt']);
            $temp = round($item['main']['temp']);
            $desc = $item['weather'][0]['main'];
            $rainChance = isset($item['pop']) ? round($item['pop'] * 100) : 0;
            
            $message .= "⏰ {$time}: {$temp}°C, {$desc}";
            if ($rainChance > 20) {
                $message .= " ({$rainChance}% rain)";
            }
            $message .= "\n";
        }

        // Farming advice
        $message .= "\n🌾 *Farming Advice:*\n";
        $message .= $this->getFarmingAdvice($current, $forecast);

        return $message;
    }

    /**
     * Generate farming advice based on weather conditions
     */
    protected function getFarmingAdvice(array $current, array $forecast): string
    {
        $advice = [];
        $temp = $current['main']['temp'];
        $humidity = $current['main']['humidity'];
        $weatherMain = strtolower($current['weather'][0]['main']);
        
        // Temperature advice
        if ($temp > 35) {
            $advice[] = "🔥 Very hot conditions - ensure adequate irrigation and consider shade for sensitive crops";
        } elseif ($temp > 30) {
            $advice[] = "☀️ Hot weather - increase watering frequency and monitor for heat stress";
        } elseif ($temp < 15) {
            $advice[] = "🥶 Cool conditions - protect sensitive crops and reduce watering";
        }

        // Weather condition advice
        if (str_contains($weatherMain, 'rain')) {
            $advice[] = "🌧️ Rainy conditions - good for planting but avoid field work to prevent soil compaction";
        } elseif (str_contains($weatherMain, 'clear') || str_contains($weatherMain, 'sun')) {
            $advice[] = "☀️ Clear skies - perfect for field work, harvesting, and drying crops";
        }

        // Humidity advice
        if ($humidity > 80) {
            $advice[] = "💧 High humidity - monitor for fungal diseases and ensure good air circulation";
        } elseif ($humidity < 40) {
            $advice[] = "🏜️ Low humidity - increase irrigation and consider mulching to retain moisture";
        }

        // Check for rain in forecast
        $rainExpected = false;
        foreach (array_slice($forecast['list'], 0, 8) as $item) {
            if (isset($item['rain']) || str_contains(strtolower($item['weather'][0]['main']), 'rain')) {
                $rainExpected = true;
                break;
            }
        }

        if ($rainExpected) {
            $advice[] = "🌦️ Rain expected in next 24 hours - plan indoor activities and prepare drainage";
        } else {
            $advice[] = "🌵 No rain expected - ensure adequate irrigation for your crops";
        }

        return implode("\n", $advice);
    }

    /**
     * Extract location from weather query message
     */
    public static function extractLocationFromMessage(string $message): ?string
    {
        // Patterns to extract location from messages like "Weather in Lagos", "Forecast for Abuja"
        $patterns = [
            '/weather\s+in\s+([a-zA-Z\s]+)/i',
            '/forecast\s+for\s+([a-zA-Z\s]+)/i',
            '/rain\s+in\s+([a-zA-Z\s]+)/i',
            '/climate\s+in\s+([a-zA-Z\s]+)/i',
            '/temperature\s+in\s+([a-zA-Z\s]+)/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                return trim($matches[1]);
            }
        }

        return null;
    }
}
