<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LocationCropRecommendationService
{
    /**
     * Get crop recommendations for a specific Nigerian location
     *
     * @param string $location
     * @return array|null
     */
    public function getLocationCropRecommendations(string $location): ?array
    {
        $location = strtolower(trim($location));
        
        // Cache key for location recommendations
        $cacheKey = "crop_recommendations_" . md5($location);
        
        return Cache::remember($cacheKey, 3600, function () use ($location) {
            return $this->generateLocationRecommendations($location);
        });
    }
    
    /**
     * Generate crop recommendations based on location
     *
     * @param string $location
     * @return array|null
     */
    private function generateLocationRecommendations(string $location): ?array
    {
        // Nigerian states and their recommended crops based on climate zones
        $locationCrops = [
            // Northern States (Sudan/Sahel Savanna)
            'kano' => [
                'primary' => ['millet', 'sorghum', 'cowpea', 'groundnut'],
                'secondary' => ['maize', 'rice', 'cotton', 'sesame'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '500-800mm',
                'season' => 'May-September (rainy season)'
            ],
            'katsina' => [
                'primary' => ['millet', 'sorghum', 'cowpea', 'groundnut'],
                'secondary' => ['maize', 'cotton', 'sesame', 'onion'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '400-700mm',
                'season' => 'May-September (rainy season)'
            ],
            'jigawa' => [
                'primary' => ['millet', 'sorghum', 'cowpea', 'rice'],
                'secondary' => ['maize', 'groundnut', 'cotton', 'wheat'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '500-800mm',
                'season' => 'May-September (rainy season)'
            ],
            'bauchi' => [
                'primary' => ['millet', 'sorghum', 'maize', 'rice'],
                'secondary' => ['cowpea', 'groundnut', 'cotton', 'yam'],
                'climate_zone' => 'Sudan/Guinea Savanna transition',
                'rainfall' => '700-1000mm',
                'season' => 'April-October'
            ],
            'gombe' => [
                'primary' => ['maize', 'sorghum', 'millet', 'rice'],
                'secondary' => ['cowpea', 'groundnut', 'cotton', 'yam'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '600-900mm',
                'season' => 'May-October'
            ],
            'yobe' => [
                'primary' => ['millet', 'sorghum', 'cowpea', 'rice'],
                'secondary' => ['maize', 'groundnut', 'cotton', 'wheat'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '300-600mm',
                'season' => 'June-September'
            ],
            'borno' => [
                'primary' => ['millet', 'sorghum', 'cowpea', 'rice'],
                'secondary' => ['maize', 'groundnut', 'cotton', 'wheat'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '300-600mm',
                'season' => 'June-September'
            ],
            'kaduna' => [
                'primary' => ['maize', 'sorghum', 'rice', 'yam'],
                'secondary' => ['millet', 'cowpea', 'groundnut', 'cotton'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1000-1300mm',
                'season' => 'April-October'
            ],
            'zamfara' => [
                'primary' => ['millet', 'sorghum', 'maize', 'rice'],
                'secondary' => ['cowpea', 'groundnut', 'cotton', 'onion'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '600-900mm',
                'season' => 'May-September'
            ],
            'kebbi' => [
                'primary' => ['rice', 'millet', 'sorghum', 'maize'],
                'secondary' => ['cowpea', 'groundnut', 'cotton', 'wheat'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '500-800mm',
                'season' => 'May-September'
            ],
            'sokoto' => [
                'primary' => ['millet', 'sorghum', 'rice', 'maize'],
                'secondary' => ['cowpea', 'groundnut', 'cotton', 'onion'],
                'climate_zone' => 'Sudan Savanna',
                'rainfall' => '500-800mm',
                'season' => 'May-September'
            ],
            
            // Middle Belt States (Guinea Savanna)
            'plateau' => [
                'primary' => ['maize', 'rice', 'yam', 'potato'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1200-1500mm',
                'season' => 'April-October'
            ],
            'nasarawa' => [
                'primary' => ['yam', 'maize', 'rice', 'cassava'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1000-1400mm',
                'season' => 'April-October'
            ],
            'benue' => [
                'primary' => ['yam', 'cassava', 'maize', 'rice'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1200-1500mm',
                'season' => 'April-October'
            ],
            'kogi' => [
                'primary' => ['yam', 'cassava', 'maize', 'rice'],
                'secondary' => ['cowpea', 'groundnut', 'sorghum', 'millet'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1200-1500mm',
                'season' => 'April-October'
            ],
            'kwara' => [
                'primary' => ['yam', 'maize', 'rice', 'cassava'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1000-1400mm',
                'season' => 'April-October'
            ],
            'niger' => [
                'primary' => ['rice', 'maize', 'yam', 'millet'],
                'secondary' => ['sorghum', 'cowpea', 'groundnut', 'cassava'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1000-1300mm',
                'season' => 'April-October'
            ],
            'fct' => [
                'primary' => ['maize', 'rice', 'yam', 'cassava'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1200-1500mm',
                'season' => 'April-October'
            ],
            'abuja' => [
                'primary' => ['maize', 'rice', 'yam', 'cassava'],
                'secondary' => ['sorghum', 'millet', 'cowpea', 'groundnut'],
                'climate_zone' => 'Guinea Savanna',
                'rainfall' => '1200-1500mm',
                'season' => 'April-October'
            ],
            
            // Southern States (Forest Zone)
            'lagos' => [
                'primary' => ['cassava', 'plantain', 'maize', 'vegetables'],
                'secondary' => ['yam', 'cocoyam', 'pepper', 'tomato'],
                'climate_zone' => 'Forest Zone',
                'rainfall' => '1500-2000mm',
                'season' => 'March-November (two seasons)'
            ],
            'ogun' => [
                'primary' => ['cassava', 'maize', 'yam', 'plantain'],
                'secondary' => ['cocoyam', 'vegetables', 'cocoa', 'oil palm'],
                'climate_zone' => 'Forest Zone',
                'rainfall' => '1200-1800mm',
                'season' => 'March-November (two seasons)'
            ],
            'oyo' => [
                'primary' => ['yam', 'maize', 'cassava', 'cocoa'],
                'secondary' => ['plantain', 'vegetables', 'cowpea', 'groundnut'],
                'climate_zone' => 'Forest/Guinea Savanna',
                'rainfall' => '1200-1600mm',
                'season' => 'March-November'
            ],
            'osun' => [
                'primary' => ['yam', 'maize', 'cassava', 'cocoa'],
                'secondary' => ['plantain', 'vegetables', 'cowpea', 'oil palm'],
                'climate_zone' => 'Forest Zone',
                'rainfall' => '1200-1600mm',
                'season' => 'March-November'
            ],
            'ondo' => [
                'primary' => ['cassava', 'yam', 'maize', 'cocoa'],
                'secondary' => ['plantain', 'oil palm', 'vegetables', 'rubber'],
                'climate_zone' => 'Forest Zone',
                'rainfall' => '1400-2000mm',
                'season' => 'March-November (two seasons)'
            ],
            'ekiti' => [
                'primary' => ['yam', 'maize', 'cassava', 'cocoa'],
                'secondary' => ['plantain', 'vegetables', 'cowpea', 'oil palm'],
                'climate_zone' => 'Forest Zone',
                'rainfall' => '1200-1600mm',
                'season' => 'March-November'
            ]
        ];
        
        // Check if location exists in our data
        if (isset($locationCrops[$location])) {
            return $locationCrops[$location];
        }
        
        // Try partial matching for common variations
        foreach ($locationCrops as $state => $data) {
            if (strpos($location, $state) !== false || strpos($state, $location) !== false) {
                return $data;
            }
        }
        
        return null;
    }
    
    /**
     * Format crop recommendations for WhatsApp response
     *
     * @param string $location
     * @param array $recommendations
     * @return string
     */
    public function formatRecommendationsForWhatsApp(string $location, array $recommendations): string
    {
        $location = ucfirst($location);
        
        $message = "🌾 *Best Crops for {$location}*\n\n";
        
        // Primary crops
        $message .= "🥇 *Primary Crops:*\n";
        foreach ($recommendations['primary'] as $crop) {
            $message .= "• " . ucfirst($crop) . "\n";
        }
        
        $message .= "\n🥈 *Secondary Crops:*\n";
        foreach ($recommendations['secondary'] as $crop) {
            $message .= "• " . ucfirst($crop) . "\n";
        }
        
        $message .= "\n📍 *Climate Zone:* " . $recommendations['climate_zone'] . "\n";
        $message .= "🌧️ *Rainfall:* " . $recommendations['rainfall'] . "\n";
        $message .= "📅 *Planting Season:* " . $recommendations['season'] . "\n\n";
        
        $message .= "💡 *Tip:* Choose crops based on your local soil conditions and market demand!\n\n";
        $message .= "🌱 *Need specific growing tips?* Ask me about any of these crops!";
        
        return $message;
    }
}
