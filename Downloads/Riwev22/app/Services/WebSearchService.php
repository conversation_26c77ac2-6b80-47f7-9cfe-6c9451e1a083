<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\ChatbotConfiguration;

class WebSearchService
{
    private ?string $apiKey;
    private ?string $searchEngineId;
    private int $cacheTime = 1800; // 30 minutes
    private int $timeout = 15;

    public function __construct()
    {
        $apiKey = ChatbotConfiguration::getValue('google_search_api_key', config('services.google.search_api_key', ''));
        $searchEngineId = ChatbotConfiguration::getValue('google_search_engine_id', config('services.google.search_engine_id', ''));

        $this->apiKey = !empty($apiKey) ? $apiKey : null;
        $this->searchEngineId = !empty($searchEngineId) ? $searchEngineId : null;

        // Update cache time from configuration
        $this->cacheTime = (int) ChatbotConfiguration::getValue('web_search_cache_time', 1800);
    }

    /**
     * Search for current agricultural commodity prices.
     */
    public function searchCommodityPrices(string $commodity, string $country = 'Nigeria'): array
    {
        try {
            // Check if web search is enabled
            if (!ChatbotConfiguration::getValue('web_search_enabled', false)) {
                return [
                    'success' => false,
                    'error' => 'Web search functionality is currently disabled',
                    'commodity' => $commodity,
                    'country' => $country
                ];
            }

            // Build search query for current market prices
            $query = $this->buildMarketPriceQuery($commodity, $country);
            
            Log::info('Searching for commodity prices', [
                'commodity' => $commodity,
                'country' => $country,
                'query' => $query
            ]);

            // Perform web search
            $searchResults = $this->performWebSearch($query);

            if (!$searchResults['success']) {
                return $searchResults;
            }

            // Extract and format price information
            $priceInfo = $this->extractPriceInformation($searchResults['results'], $commodity, $country);

            return [
                'success' => true,
                'commodity' => $commodity,
                'country' => $country,
                'price_info' => $priceInfo,
                'search_query' => $query,
                'sources' => $this->extractSources($searchResults['results']),
                'last_updated' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Web search for commodity prices failed', [
                'commodity' => $commodity,
                'country' => $country,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to search for current market prices: ' . $e->getMessage(),
                'commodity' => $commodity,
                'country' => $country
            ];
        }
    }

    /**
     * Perform general web search for agricultural information.
     */
    public function searchAgricultureInfo(string $query): array
    {
        try {
            // Add agriculture-specific terms to improve relevance
            $enhancedQuery = $this->enhanceAgricultureQuery($query);
            
            Log::info('Searching for agriculture information', [
                'original_query' => $query,
                'enhanced_query' => $enhancedQuery
            ]);

            // Perform web search
            $searchResults = $this->performWebSearch($enhancedQuery);

            if (!$searchResults['success']) {
                return $searchResults;
            }

            // Format results for agricultural context
            $formattedResults = $this->formatAgricultureResults($searchResults['results'], $query);

            return [
                'success' => true,
                'query' => $query,
                'enhanced_query' => $enhancedQuery,
                'results' => $formattedResults,
                'sources' => $this->extractSources($searchResults['results']),
                'last_updated' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Web search for agriculture info failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to search for agricultural information: ' . $e->getMessage(),
                'query' => $query
            ];
        }
    }

    /**
     * Perform web search using Google Custom Search API.
     */
    private function performWebSearch(string $query): array
    {
        if ($this->apiKey === null || $this->searchEngineId === null) {
            return [
                'success' => false,
                'error' => 'Google Custom Search API not configured'
            ];
        }

        $cacheKey = 'web_search_' . md5($query);
        
        return Cache::remember($cacheKey, $this->cacheTime, function () use ($query) {
            try {
                $response = Http::timeout($this->timeout)
                    ->get('https://www.googleapis.com/customsearch/v1', [
                        'key' => $this->apiKey,
                        'cx' => $this->searchEngineId,
                        'q' => $query,
                        'num' => 10,
                        'dateRestrict' => 'm1', // Last month for fresh data
                        'safe' => 'active',
                        'lr' => 'lang_en'
                    ]);

                if (!$response->successful()) {
                    throw new \Exception('Google Search API request failed: ' . $response->body());
                }

                $data = $response->json();

                return [
                    'success' => true,
                    'results' => $data['items'] ?? [],
                    'total_results' => $data['searchInformation']['totalResults'] ?? 0,
                    'search_time' => $data['searchInformation']['searchTime'] ?? 0
                ];

            } catch (\Exception $e) {
                Log::error('Google Custom Search API error', [
                    'query' => $query,
                    'error' => $e->getMessage()
                ]);

                return [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        });
    }

    /**
     * Build search query for market prices.
     */
    private function buildMarketPriceQuery(string $commodity, string $country): string
    {
        $currentDate = now()->format('Y');
        
        // Build comprehensive search query
        $queries = [
            "current {$commodity} price {$country} {$currentDate}",
            "latest {$commodity} market price {$country} today",
            "{$commodity} commodity price {$country} current",
            "{$commodity} agricultural price {$country} market"
        ];

        // Use the most specific query
        return $queries[0];
    }

    /**
     * Enhance agriculture query with relevant terms.
     */
    private function enhanceAgricultureQuery(string $query): string
    {
        $agricultureTerms = ['farming', 'agriculture', 'crop', 'agricultural'];
        
        // Check if query already contains agriculture terms
        $hasAgricultureTerm = false;
        foreach ($agricultureTerms as $term) {
            if (stripos($query, $term) !== false) {
                $hasAgricultureTerm = true;
                break;
            }
        }

        // Add agriculture context if not present
        if (!$hasAgricultureTerm) {
            $query = "agriculture farming " . $query;
        }

        return $query;
    }

    /**
     * Detect if a message is asking for current market prices.
     */
    public function isMarketPriceQuery(string $message): bool
    {
        $message = strtolower($message);

        $priceKeywords = [
            'current price', 'latest price', 'today price', 'market price today',
            'price today', 'current market price', 'latest market price',
            'price now', 'current cost', 'today cost', 'market rate',
            'current rate', 'latest rate', 'price update', 'market update',
            'market price', 'price', 'cost', 'rate', 'pricing', 'how much'
        ];

        $commodityKeywords = [
            'rice', 'maize', 'corn', 'wheat', 'cassava', 'yam', 'plantain',
            'tomato', 'pepper', 'onion', 'beans', 'cowpea', 'soybean',
            'groundnut', 'peanut', 'millet', 'sorghum', 'cocoa', 'palm oil',
            'ginger', 'garlic', 'potato', 'sweet potato', 'okra', 'cucumber'
        ];

        // Check for price keywords
        $hasPriceKeyword = false;
        foreach ($priceKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $hasPriceKeyword = true;
                break;
            }
        }

        // Check for commodity keywords
        $hasCommodityKeyword = false;
        foreach ($commodityKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $hasCommodityKeyword = true;
                break;
            }
        }

        // If user asks for general market price without specific commodity,
        // we can still help by suggesting popular commodities or asking for clarification
        if ($hasPriceKeyword && !$hasCommodityKeyword) {
            // Check for general market queries
            $generalMarketKeywords = ['market', 'commodity', 'agricultural', 'farm', 'crop'];
            foreach ($generalMarketKeywords as $keyword) {
                if (strpos($message, $keyword) !== false) {
                    return true; // Allow general market price queries
                }
            }
        }

        return $hasPriceKeyword && $hasCommodityKeyword;
    }

    /**
     * Extract commodity name from message.
     */
    public function extractCommodity(string $message): ?string
    {
        $message = strtolower($message);

        $commodities = [
            // Cereals and grains
            'rice', 'maize', 'corn', 'wheat', 'barley', 'oats', 'rye', 'millet', 'sorghum', 'quinoa',
            // Legumes and pulses
            'beans', 'cowpea', 'cowpeas', 'black-eyed peas', 'soybean', 'soybeans', 'lentils', 'chickpeas', 'peas',
            'groundnut', 'groundnuts', 'peanut', 'peanuts', 'pigeon peas',
            // Root crops and tubers
            'cassava', 'yam', 'yams', 'potato', 'potatoes', 'sweet potato', 'sweet potatoes', 'taro',
            // Vegetables
            'tomato', 'tomatoes', 'pepper', 'peppers', 'onion', 'onions', 'garlic', 'ginger',
            'okra', 'cucumber', 'cucumbers', 'lettuce', 'cabbage', 'carrot', 'carrots',
            // Fruits
            'plantain', 'plantains', 'banana', 'bananas', 'mango', 'mangoes', 'orange', 'oranges',
            'apple', 'apples', 'avocado', 'avocados', 'pineapple', 'pineapples',
            // Cash crops
            'cocoa', 'coffee', 'tea', 'cotton', 'sugarcane', 'tobacco',
            // Oil crops
            'palm oil', 'oil palm', 'sunflower', 'sesame', 'coconut',
            // Spices and herbs
            'turmeric', 'coriander', 'cumin', 'cardamom', 'cinnamon'
        ];

        // Use word boundaries to avoid partial matches (e.g., "rice" in "price")
        foreach ($commodities as $commodity) {
            if (preg_match('/\b' . preg_quote($commodity, '/') . '\b/', $message)) {
                // Return the base form for consistency
                return $this->normalizeommodityName($commodity);
            }
        }

        return null;
    }

    /**
     * Normalize commodity name to base form
     */
    private function normalizeommodityName(string $commodity): string
    {
        $normalizations = [
            'cowpeas' => 'cowpea',
            'black-eyed peas' => 'cowpea',
            'soybeans' => 'soybean',
            'groundnuts' => 'groundnut',
            'peanuts' => 'peanut',
            'yams' => 'yam',
            'potatoes' => 'potato',
            'sweet potatoes' => 'sweet potato',
            'tomatoes' => 'tomato',
            'peppers' => 'pepper',
            'onions' => 'onion',
            'cucumbers' => 'cucumber',
            'carrots' => 'carrot',
            'plantains' => 'plantain',
            'bananas' => 'banana',
            'mangoes' => 'mango',
            'oranges' => 'orange',
            'apples' => 'apple',
            'avocados' => 'avocado',
            'pineapples' => 'pineapple'
        ];

        return $normalizations[$commodity] ?? $commodity;
    }

    /**
     * Get popular commodities for general market price queries.
     */
    public function getPopularCommodities(): array
    {
        return [
            'rice', 'maize', 'tomato', 'beans', 'yam', 'cassava', 'plantain', 'pepper'
        ];
    }

    /**
     * Search for general market information when no specific commodity is mentioned.
     */
    public function searchGeneralMarketInfo(string $country = 'Nigeria'): array
    {
        try {
            $query = "current agricultural commodity prices market {$country} today";
            $result = $this->searchAgricultureInfo($query);

            if ($result['success']) {
                return [
                    'success' => true,
                    'type' => 'general_market',
                    'country' => $country,
                    'search_query' => $query,
                    'results' => $result['results'],
                    'sources' => $this->extractSources($result['results']),
                    'summary' => $this->formatGeneralMarketSummary($result['results'], $country),
                    'popular_commodities' => $this->getPopularCommodities(),
                    'last_updated' => now()->toDateTimeString()
                ];
            }

            return [
                'success' => false,
                'error' => $result['error'] ?? 'Failed to fetch general market information'
            ];

        } catch (\Exception $e) {
            Log::error('General market search failed', [
                'country' => $country,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to search general market information: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Format general market summary from search results.
     */
    private function formatGeneralMarketSummary(array $results, string $country): string
    {
        if (empty($results)) {
            return "I couldn't find current market price information for {$country} at the moment. You can ask me about specific commodities like rice, maize, tomato, beans, or other crops.";
        }

        $summary = "Here's the latest market information I found for {$country}:\n\n";

        foreach (array_slice($results, 0, 3) as $index => $result) {
            $title = $result['title'] ?? '';
            $snippet = $result['snippet'] ?? '';
            $source = parse_url($result['link'] ?? '', PHP_URL_HOST) ?? 'Unknown';

            $summary .= "**Source " . ($index + 1) . " ({$source}):**\n";
            $summary .= $snippet . "\n\n";
        }

        $summary .= "For specific commodity prices, you can ask me about: " . implode(', ', $this->getPopularCommodities()) . "\n";
        $summary .= "Example: 'What is the current price of rice?' or 'How much is maize today?'";

        return $summary;
    }

    /**
     * Extract price information from search results.
     */
    private function extractPriceInformation(array $results, string $commodity, string $country): array
    {
        $priceInfo = [
            'summary' => '',
            'prices' => [],
            'trends' => '',
            'sources_count' => count($results)
        ];

        if (empty($results)) {
            $priceInfo['summary'] = "No current price information found for {$commodity} in {$country}.";
            return $priceInfo;
        }

        // Extract relevant information from search results
        $relevantResults = [];
        foreach ($results as $result) {
            $title = $result['title'] ?? '';
            $snippet = $result['snippet'] ?? '';
            $link = $result['link'] ?? '';

            // Look for price patterns in title and snippet
            $pricePattern = '/(?:₦|NGN|N)\s*[\d,]+(?:\.\d{2})?|[\d,]+(?:\.\d{2})?\s*(?:naira|NGN|₦)/i';

            if (preg_match($pricePattern, $title . ' ' . $snippet)) {
                $relevantResults[] = [
                    'title' => $title,
                    'snippet' => $snippet,
                    'link' => $link,
                    'source' => parse_url($link, PHP_URL_HOST) ?? 'Unknown'
                ];
            }
        }

        // Build summary from relevant results
        if (!empty($relevantResults)) {
            $priceInfo['summary'] = "Based on recent market data, here's what I found about {$commodity} prices in {$country}:\n\n";

            foreach (array_slice($relevantResults, 0, 3) as $index => $result) {
                $priceInfo['summary'] .= "**Source " . ($index + 1) . " ({$result['source']}):**\n";
                $priceInfo['summary'] .= $result['snippet'] . "\n\n";

                $priceInfo['prices'][] = [
                    'source' => $result['source'],
                    'title' => $result['title'],
                    'snippet' => $result['snippet'],
                    'link' => $result['link']
                ];
            }
        } else {
            $priceInfo['summary'] = "I found some information about {$commodity} in {$country}, but specific current prices weren't clearly available in the search results. ";
            $priceInfo['summary'] .= "You may want to check local markets or agricultural commodity exchanges for the most current pricing.";
        }

        return $priceInfo;
    }

    /**
     * Format agriculture search results.
     */
    private function formatAgricultureResults(array $results, string $originalQuery): array
    {
        $formattedResults = [];

        foreach (array_slice($results, 0, 5) as $result) {
            $formattedResults[] = [
                'title' => $result['title'] ?? '',
                'snippet' => $result['snippet'] ?? '',
                'link' => $result['link'] ?? '',
                'source' => parse_url($result['link'] ?? '', PHP_URL_HOST) ?? 'Unknown',
                'relevance_score' => $this->calculateRelevanceScore($result, $originalQuery)
            ];
        }

        // Sort by relevance score
        usort($formattedResults, function ($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        return $formattedResults;
    }

    /**
     * Calculate relevance score for search results.
     */
    private function calculateRelevanceScore(array $result, string $query): float
    {
        $score = 0.0;
        $title = strtolower($result['title'] ?? '');
        $snippet = strtolower($result['snippet'] ?? '');
        $queryWords = explode(' ', strtolower($query));

        // Score based on query word matches in title (higher weight)
        foreach ($queryWords as $word) {
            if (strlen($word) > 2) { // Skip short words
                if (strpos($title, $word) !== false) {
                    $score += 2.0;
                }
                if (strpos($snippet, $word) !== false) {
                    $score += 1.0;
                }
            }
        }

        // Bonus for agricultural sources
        $agriculturalSources = ['fao.org', 'usda.gov', 'worldbank.org', 'cgiar.org', 'ifpri.org'];
        $link = $result['link'] ?? '';
        foreach ($agriculturalSources as $source) {
            if (strpos($link, $source) !== false) {
                $score += 3.0;
                break;
            }
        }

        return $score;
    }

    /**
     * Extract sources from search results.
     */
    private function extractSources(array $results): array
    {
        $sources = [];

        foreach ($results as $result) {
            $link = $result['link'] ?? '';
            $host = parse_url($link, PHP_URL_HOST) ?? 'Unknown';

            if (!in_array($host, $sources)) {
                $sources[] = $host;
            }
        }

        return array_slice($sources, 0, 5); // Limit to top 5 sources
    }

    /**
     * Test web search configuration.
     */
    public function testConfiguration(): array
    {
        try {
            if ($this->apiKey === null || $this->searchEngineId === null) {
                return [
                    'success' => false,
                    'message' => 'Google Custom Search API not configured. Please set API key and search engine ID.',
                    'configured' => false
                ];
            }

            // Test with a simple agriculture query
            $testResult = $this->searchAgricultureInfo('agriculture farming techniques');

            return [
                'success' => $testResult['success'],
                'message' => $testResult['success']
                    ? 'Web search configuration is working correctly'
                    : 'Web search test failed: ' . ($testResult['error'] ?? 'Unknown error'),
                'configured' => true,
                'test_results' => $testResult['success'] ? count($testResult['results'] ?? []) : 0
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Web search test failed: ' . $e->getMessage(),
                'configured' => $this->apiKey !== null && $this->searchEngineId !== null
            ];
        }
    }
}
