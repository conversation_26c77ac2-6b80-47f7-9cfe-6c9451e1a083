<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Services\ChatbotService;
use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WhatsAppWebhookController extends Controller
{
    private ChatbotService $chatbotService;
    private WhatsAppService $whatsappService;

    public function __construct(ChatbotService $chatbotService, WhatsAppService $whatsappService)
    {
        $this->chatbotService = $chatbotService;
        $this->whatsappService = $whatsappService;
    }

    /**
     * Verify webhook for WhatsApp Business API setup.
     * This endpoint is called by WhatsApp to verify the webhook URL.
     */
    public function verify(Request $request): JsonResponse|string
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        Log::info('WhatsApp webhook verification attempt', [
            'mode' => $mode,
            'token' => $token ? substr($token, 0, 10) . '...' : null,
            'challenge' => $challenge,
            'user_agent' => $request->header('User-Agent'),
            'headers' => $request->headers->all(),
        ]);

        // Check if this is a verification request
        if ($mode === 'subscribe' && $token === config('services.whatsapp.webhook_verify_token')) {
            Log::info('WhatsApp webhook verification successful', [
                'challenge' => $challenge,
                'returning' => $challenge
            ]);

            // Return plain text response as required by Meta
            return response($challenge)
                ->header('Content-Type', 'text/plain')
                ->header('Cache-Control', 'no-cache');
        }

        Log::warning('WhatsApp webhook verification failed', [
            'expected_token' => config('services.whatsapp.webhook_verify_token') ? 'configured' : 'not_configured',
            'received_mode' => $mode,
        ]);

        return response()->json(['error' => 'Forbidden'], 403);
    }

    /**
     * Handle incoming WhatsApp webhook events.
     * This endpoint receives messages, status updates, and other events from WhatsApp.
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            // Check if WhatsApp integration is enabled
            if (!$this->isWhatsAppEnabled()) {
                Log::info('WhatsApp webhook received but integration is disabled');
                return response()->json(['status' => 'disabled', 'message' => 'WhatsApp integration is currently disabled']);
            }

            // Validate signature if enabled
            if (config('services.whatsapp.validate_signature', true)) {
                if (!$this->validateSignature($request)) {
                    Log::warning('WhatsApp webhook signature validation failed');
                    return response()->json(['error' => 'Invalid signature'], 403);
                }
            }

            $payload = $request->all();
            Log::info('WhatsApp webhook received', ['payload' => $payload]);

            // Process the webhook payload
            if (isset($payload['entry'])) {
                foreach ($payload['entry'] as $entry) {
                    if (isset($entry['changes'])) {
                        foreach ($entry['changes'] as $change) {
                            if ($change['field'] === 'messages' && isset($change['value']['messages'])) {
                                foreach ($change['value']['messages'] as $message) {
                                    $this->processIncomingMessage($message, $change['value']);
                                }
                            }
                        }
                    }
                }
            }

            return response()->json(['status' => 'success', 'received' => true]);

        } catch (\Exception $e) {
            Log::error('WhatsApp webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Handle incoming message events from WhatsApp.
     */
    private function handleMessageEvent(array $messageData): void
    {
        try {
            // Check if there are messages in the event
            if (!isset($messageData['messages']) || empty($messageData['messages'])) {
                return;
            }

            foreach ($messageData['messages'] as $message) {
                $this->processIncomingMessage($message, $messageData);
            }

        } catch (\Exception $e) {
            Log::error('Error handling WhatsApp message event', [
                'error' => $e->getMessage(),
                'message_data' => $messageData,
            ]);
        }
    }

    /**
     * Process a single incoming WhatsApp message.
     */
    private function processIncomingMessage(array $message, array $messageData): void
    {
        try {
            $phoneNumber = $message['from'] ?? null;
            $messageType = $message['type'] ?? 'text';

            if (!$phoneNumber) {
                Log::warning('WhatsApp message missing phone number', ['message' => $message]);
                return;
            }

            Log::info('Processing WhatsApp message', [
                'from' => $phoneNumber,
                'type' => $messageType,
                'message_id' => $message['id'] ?? null,
            ]);

            // Find or create user for this WhatsApp number
            $user = $this->findOrCreateWhatsAppUser($phoneNumber, $messageData);

            if (!$user) {
                Log::error('Failed to find or create WhatsApp user', ['phone' => $phoneNumber]);
                return;
            }

            // Process based on message type
            if ($messageType === 'image') {
                $this->processImageMessage($message, $user, $phoneNumber);
            } elseif ($messageType === 'text') {
                $this->processTextMessage($message, $user, $phoneNumber);
            } else {
                $this->handleUnsupportedMessageType($messageType, $phoneNumber);
            }

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp message', [
                'error' => $e->getMessage(),
                'message' => $message,
            ]);
        }
    }

    /**
     * Process text messages with INSTANT response (milliseconds).
     */
    private function processTextMessage(array $message, $user, string $phoneNumber): void
    {
        $messageText = $message['text']['body'] ?? null;

        if (!$messageText) {
            Log::warning('WhatsApp text message missing body', ['message' => $message]);
            return;
        }

        // Check for signup commands first (handle synchronously for immediate response)
        $messageUpper = strtoupper(trim($messageText));

        if ($messageUpper === 'SIGNUP' && $user->is_whatsapp_guest === true) {
            $botMessage = $this->handleSignupCommand($user, $phoneNumber);
            $this->sendWhatsAppResponse($phoneNumber, $botMessage);
            return;
        } elseif (strpos($messageUpper, 'SIGNUP EMAIL:') === 0 && $user->is_whatsapp_guest === true) {
            $email = trim(substr($messageText, 13)); // Extract email after "SIGNUP EMAIL:"
            $botMessage = $this->handleSignupWithEmail($user, $email, $phoneNumber);
            $this->sendWhatsAppResponse($phoneNumber, $botMessage);
            return;
        }

        // INSTANT RESPONSE SYSTEM - Send immediate response in milliseconds
        $instantResponse = $this->getInstantResponse($messageText, $user);

        if ($instantResponse) {
            // Send instant cached/pre-computed response immediately
            $this->sendWhatsAppResponse($phoneNumber, $instantResponse);

            // Then enhance with AI in background (optional)
            \App\Jobs\EnhanceWhatsAppResponse::dispatch($user->id, $messageText, $phoneNumber, $instantResponse)
                ->onQueue('low')->delay(now()->addSeconds(2));
        } else {
            // Process with optimized fast AI (no acknowledgment message to avoid repetition)
            \App\Jobs\ProcessWhatsAppMessageFast::dispatch($user->id, $messageText, $phoneNumber)
                ->onQueue('high');
        }
    }

    /**
     * Process image messages for plant analysis.
     */
    private function processImageMessage(array $message, $user, string $phoneNumber): void
    {
        try {
            $imageData = $message['image'] ?? null;

            if (!$imageData) {
                Log::warning('WhatsApp image message missing image data', ['message' => $message]);
                $this->sendWhatsAppResponse($phoneNumber,
                    "❌ Sorry, I couldn't process your image. Please try sending it again.");
                return;
            }

            $mediaId = $imageData['id'] ?? null;
            $caption = $imageData['caption'] ?? 'Please analyze this plant image for pests and diseases';

            if (!$mediaId) {
                Log::warning('WhatsApp image message missing media ID', ['image_data' => $imageData]);
                $this->sendWhatsAppResponse($phoneNumber,
                    "❌ Sorry, I couldn't access your image. Please try sending it again.");
                return;
            }

            Log::info('Processing WhatsApp image message', [
                'from' => $phoneNumber,
                'media_id' => $mediaId,
                'caption' => $caption,
                'user_id' => $user->id
            ]);

            // Send immediate acknowledgment and typing indicator
            $this->sendWhatsAppResponse($phoneNumber,
                "📸 Image received! I'm analyzing your plant now... 🌱");

            $this->sendTypingIndicator($phoneNumber);

            // Process image analysis asynchronously for better performance
            \App\Jobs\ProcessWhatsAppImageAnalysis::dispatch($user->id, $mediaId, $caption, $phoneNumber)
                ->onQueue('high');

        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp image message', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'phone_number' => $phoneNumber,
                'user_id' => $user->id ?? null
            ]);

            $this->sendWhatsAppResponse($phoneNumber,
                "❌ Sorry, there was an error analyzing your image. Please try again later.");
        }
    }

    /**
     * Handle unsupported message types.
     */
    private function handleUnsupportedMessageType(string $messageType, string $phoneNumber): void
    {
        $supportedTypes = [
            'document' => "📄 I can't analyze documents yet. Please send an image of your plant for analysis.",
            'audio' => "🎵 I can't analyze audio messages. Please send an image of your plant for analysis.",
            'video' => "🎥 I can't analyze videos yet. Please send a clear image of your plant for analysis.",
            'sticker' => "😊 Thanks for the sticker! Please send an image of your plant if you need analysis.",
            'location' => "📍 Thanks for sharing your location! Please send an image of your plant for analysis."
        ];

        $message = $supportedTypes[$messageType] ??
                  "❓ I can only analyze text messages and plant images. Please send an image of your plant for analysis.";

        $this->sendWhatsAppResponse($phoneNumber, $message);
    }

    /**
     * Send WhatsApp response with error handling.
     */
    private function sendWhatsAppResponse(string $phoneNumber, string $message): void
    {
        $sendResult = $this->whatsappService->sendMessage($phoneNumber, $message);

        if ($sendResult['success']) {
            Log::info('WhatsApp response sent successfully', [
                'phone' => $phoneNumber,
                'message_id' => $sendResult['data']['messages'][0]['id'] ?? null,
                'response_length' => strlen($message),
            ]);
        } else {
            Log::error('Failed to send WhatsApp response', [
                'phone' => $phoneNumber,
                'error' => $sendResult['message'],
            ]);
        }
    }

    /**
     * Get instant response for common queries (millisecond response time).
     */
    private function getInstantResponse(string $message, $user): ?string
    {
        $messageLower = strtolower(trim($message));

        // Cache key for user-specific responses
        $cacheKey = "instant_response_" . md5($messageLower . $user->id);

        // Try cache first (Redis lookup ~1ms)
        $cached = \Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }

        // Pre-computed instant responses for common queries
        $instantResponses = [
            // Greetings (most common)
            'hello' => $this->getWelcomeMessage(),
            'hi' => $this->getWelcomeMessage(),
            'hey' => $this->getWelcomeMessage(),
            'good morning' => "🌅 Good morning! Ready to make your farm more productive today?\n\n" . $this->getQuickStartGuide(),
            'good afternoon' => "☀️ Good afternoon! How can I assist with your farming today?\n\n" . $this->getQuickStartGuide(),
            'good evening' => "🌆 Good evening! What farming topic can I help you with?\n\n" . $this->getQuickStartGuide(),

            // Help and guidance
            'help' => $this->getDetailedHelpMenu(),
            'menu' => $this->getMainMenu(),
            'guide' => $this->getCompleteUserGuide(),
            'how to use' => $this->getCompleteUserGuide(),
            'how do i use this' => $this->getCompleteUserGuide(),
            'what can you do' => $this->getCapabilitiesOverview(),
            'examples' => $this->getExampleQueries(),

            // Weather (very common)
            'weather' => $this->getWeatherGuidance(),
            'rain' => "🌧️ Rain predictions depend on your location. Ask: 'Will it rain in [your area]?' for specific forecasts.\n\n" . $this->getWeatherExamples(),
            'forecast' => $this->getWeatherGuidance(),
            'temperature' => $this->getWeatherGuidance(),

            // Market prices (common)
            'price' => $this->getMarketPriceGuidance(),
            'market' => $this->getMarketPriceGuidance(),
            'cost' => $this->getMarketPriceGuidance(),
            'sell' => $this->getMarketPriceGuidance(),

            // Crop guidance
            'plant' => $this->getCropGuidance(),
            'grow' => $this->getCropGuidance(),
            'farming' => $this->getCropGuidance(),
            'crop' => $this->getCropGuidance(),
            'harvest' => $this->getCropGuidance(),

            // Beginner guidance
            'new farmer' => $this->getBeginnerGuidance(),
            'beginner' => $this->getBeginnerGuidance(),
            'start farming' => $this->getBeginnerGuidance(),
            'how to farm' => $this->getBeginnerGuidance(),

            // Photo guidance
            'photo' => $this->getPhotoGuidance(),
            'image' => $this->getPhotoGuidance(),
            'picture' => $this->getPhotoGuidance(),
            'disease' => $this->getPhotoGuidance(),
            'pest' => $this->getPhotoGuidance(),

            // Quick crop info
            'maize' => "🌽 *Maize Farming Quick Tips*\n\n🌱 Plant during rainy season\n💧 Needs 500-800mm rainfall\n🌡️ Grows best at 18-27°C\n⏰ Harvest in 3-4 months\n\nNeed specific advice? Ask away!",
            'rice' => "🌾 *Rice Farming Basics*\n\n💧 Needs flooded fields\n🌡️ Optimal temp: 20-35°C\n⏰ 3-6 months to harvest\n🌱 Plant in nursery first\n\nWhat specific rice question do you have?",
            'beans' => "🫘 *Bean Farming Tips*\n\n🌱 Plant after last frost\n💧 Moderate water needs\n🌡️ Grows well 15-25°C\n⏰ Ready in 2-3 months\n\nAny specific bean questions?",

            // Insurance & finance
            'insurance' => "🛡️ *Riwe Insurance Services*\n\n✅ Crop insurance\n✅ Livestock coverage\n✅ Weather protection\n✅ Equipment insurance\n\nInterested in coverage? I can connect you with our team!",
            'loan' => "💳 *Riwe Financial Services*\n\n💰 Farm loans\n🌾 Input financing\n📱 Mobile payments\n📊 Credit scoring\n\nNeed financing? Let me help you explore options!",

            // Common questions
            'thank you' => "🙏 You're welcome! I'm always here to help with your farming needs. Feel free to ask anything else!",
            'thanks' => "😊 Happy to help! Got more farming questions? I'm here for you 24/7!",
            'bye' => "👋 Goodbye! Remember, I'm here whenever you need farming advice. Have a great day!",
            'goodbye' => "🌾 Take care! Come back anytime for farming guidance. Wishing you a productive harvest!",
        ];

        // Check for exact matches first
        if (isset($instantResponses[$messageLower])) {
            $response = $instantResponses[$messageLower];
            \Cache::put($cacheKey, $response, now()->addHours(1)); // Cache for 1 hour
            return $response;
        }

        // Check for partial matches (contains keywords)
        foreach ($instantResponses as $keyword => $response) {
            if (str_contains($messageLower, $keyword)) {
                \Cache::put($cacheKey, $response, now()->addHours(1));
                return $response;
            }
        }

        // No instant response available
        return null;
    }

    /**
     * Send typing indicator to show bot is processing.
     */
    private function sendTypingIndicator(string $phoneNumber): void
    {
        try {
            $result = $this->whatsappService->sendTypingIndicator($phoneNumber);

            if ($result['success']) {
                Log::debug('Typing indicator sent', ['phone' => $phoneNumber]);
            } else {
                Log::warning('Failed to send typing indicator', [
                    'phone' => $phoneNumber,
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('Exception sending typing indicator', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Download and analyze WhatsApp image.
     */
    private function analyzeWhatsAppImage(string $mediaId, string $caption, $user): array
    {
        try {
            // Download the image from WhatsApp
            $downloadResult = $this->downloadWhatsAppMedia($mediaId);

            if (!$downloadResult['success']) {
                return [
                    'success' => false,
                    'error' => 'Failed to download image: ' . $downloadResult['error']
                ];
            }

            $imagePath = $downloadResult['path'];

            // Analyze the image using the existing service
            $pestDetectionService = app(\App\Services\PestDiseaseDetectionService::class);
            $analysisResult = $pestDetectionService->analyzeImage($imagePath, $caption, $user);

            // Clean up the downloaded file
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            return $analysisResult;

        } catch (\Exception $e) {
            Log::error('Error analyzing WhatsApp image', [
                'media_id' => $mediaId,
                'error' => $e->getMessage(),
                'user_id' => $user->id ?? null
            ]);

            return [
                'success' => false,
                'error' => 'Image analysis failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Download media from WhatsApp.
     */
    private function downloadWhatsAppMedia(string $mediaId): array
    {
        try {
            $accessToken = config('services.whatsapp.access_token');
            $baseUrl = config('services.whatsapp.base_url', 'https://graph.facebook.com/v18.0');

            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp access token not configured'
                ];
            }

            // Step 1: Get media URL
            $mediaResponse = \Illuminate\Support\Facades\Http::withToken($accessToken)
                ->get("{$baseUrl}/{$mediaId}");

            if (!$mediaResponse->successful()) {
                return [
                    'success' => false,
                    'error' => 'Failed to get media info: ' . $mediaResponse->body()
                ];
            }

            $mediaData = $mediaResponse->json();
            $mediaUrl = $mediaData['url'] ?? null;

            if (!$mediaUrl) {
                return [
                    'success' => false,
                    'error' => 'Media URL not found in response'
                ];
            }

            // Step 2: Download the actual media file
            $fileResponse = \Illuminate\Support\Facades\Http::withToken($accessToken)
                ->timeout(30)
                ->get($mediaUrl);

            if (!$fileResponse->successful()) {
                return [
                    'success' => false,
                    'error' => 'Failed to download media file: ' . $fileResponse->body()
                ];
            }

            // Step 3: Save the file temporarily
            $fileName = 'whatsapp_' . $mediaId . '_' . time() . '.jpg';
            $tempPath = storage_path('app/temp/whatsapp/' . $fileName);

            // Create directory if it doesn't exist
            $tempDir = dirname($tempPath);
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            file_put_contents($tempPath, $fileResponse->body());

            Log::info('WhatsApp media downloaded successfully', [
                'media_id' => $mediaId,
                'file_path' => $tempPath,
                'file_size' => filesize($tempPath)
            ]);

            return [
                'success' => true,
                'path' => $tempPath,
                'size' => filesize($tempPath)
            ];

        } catch (\Exception $e) {
            Log::error('Error downloading WhatsApp media', [
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Download failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Format image analysis result for WhatsApp.
     */
    private function formatImageAnalysisForWhatsApp(array $analysisResult): string
    {
        try {
            $message = "🔍 *Ije's Plant Analysis* 🌱\n\n";

            // Add confidence level
            if (isset($analysisResult['confidence'])) {
                $confidence = $analysisResult['confidence'] * 100;
                $message .= "📊 *Confidence:* {$confidence}%\n\n";
            }

            // Add main analysis message
            if (isset($analysisResult['message'])) {
                // Clean up the message for WhatsApp (remove excessive formatting)
                $cleanMessage = $analysisResult['message'];
                $cleanMessage = preg_replace('/\*\*(.*?)\*\*/', '*$1*', $cleanMessage); // Convert ** to *
                $cleanMessage = preg_replace('/#{1,6}\s/', '', $cleanMessage); // Remove markdown headers
                $cleanMessage = str_replace(['🔍 **Ije\'s Analysis** 🎯', '(Very High Confidence)'], '', $cleanMessage);
                $cleanMessage = trim($cleanMessage);

                $message .= $cleanMessage . "\n\n";
            }

            // Add structured analysis if available
            if (isset($analysisResult['analysis']) && is_array($analysisResult['analysis'])) {
                $analysis = $analysisResult['analysis'];

                if (isset($analysis['plant_type'])) {
                    $message .= "🌿 *Plant:* " . $analysis['plant_type'] . "\n";
                }

                if (isset($analysis['health_status'])) {
                    $message .= "💚 *Health:* " . $analysis['health_status'] . "\n";
                }

                if (isset($analysis['severity_level'])) {
                    $message .= "⚠️ *Severity:* " . $analysis['severity_level'] . "\n";
                }

                $message .= "\n";
            }

            // Add footer
            $message .= "💡 *Need more help?* Send another image or ask me questions!\n";
            $message .= "🌾 *Powered by Ije AI - Your Farming Assistant*";

            // Ensure message is not too long for WhatsApp (4096 character limit)
            if (strlen($message) > 4000) {
                $message = substr($message, 0, 3950) . "...\n\n💡 *Full analysis available on Riwe platform*";
            }

            return $message;

        } catch (\Exception $e) {
            Log::error('Error formatting WhatsApp analysis response', [
                'error' => $e->getMessage(),
                'analysis_result' => $analysisResult
            ]);

            return "🔍 *Analysis Complete* 🌱\n\nI've analyzed your plant image. " .
                   "For detailed results, please visit the Riwe platform or try sending the image again.";
        }
    }

    /**
     * Find existing user or create a new WhatsApp guest user.
     */
    private function findOrCreateWhatsAppUser(string $phoneNumber, array $messageData): ?User
    {
        try {
            // Clean phone number (remove whatsapp: prefix if present)
            $cleanPhone = str_replace('whatsapp:', '', $phoneNumber);
            $cleanPhone = preg_replace('/[^0-9+]/', '', $cleanPhone);

            // Try to find existing user by phone number
            $user = User::where('phone_number', $cleanPhone)
                ->orWhere('phone_number', $phoneNumber)
                ->first();

            if ($user) {
                // Don't mark existing registered users as WhatsApp guests
                // Only mark as WhatsApp user if they are currently guests
                if ($user->is_whatsapp_guest === null || $user->is_whatsapp_guest === true) {
                    // Keep existing guest status or set to false if they have a proper email
                    $isGuest = !filter_var($user->email, FILTER_VALIDATE_EMAIL) ||
                              str_contains($user->email, '@guest.riwe.io');
                    $user->update(['is_whatsapp_guest' => $isGuest]);
                }
                return $user;
            }

            // Check if WhatsApp guest creation is enabled
            if (!config('chatbot.whatsapp.auto_create_users', true)) {
                Log::info('WhatsApp guest user creation disabled', ['phone' => $phoneNumber]);
                return null;
            }

            // Create new WhatsApp guest user
            $guestPrefix = config('chatbot.whatsapp.guest_prefix', 'WhatsApp User');
            $userName = $guestPrefix . ' ' . substr($cleanPhone, -4);

            // Get contact name from WhatsApp if available
            if (isset($messageData['contacts'][0]['profile']['name'])) {
                $userName = $messageData['contacts'][0]['profile']['name'];
            }

            $user = User::create([
                'name' => $userName,
                'email' => 'whatsapp_' . $cleanPhone . '@guest.riwe.io',
                'phone_number' => $cleanPhone,
                'password' => bcrypt(Str::random(32)), // Random password
                'is_whatsapp_guest' => true,
                'email_verified_at' => now(), // Auto-verify WhatsApp users
                'preferred_currency' => 'NGN',
                'location' => 'Nigeria', // Default location
            ]);

            // Assign default role
            $defaultRole = Role::where('name', 'user')->first();
            if ($defaultRole) {
                $user->roles()->attach($defaultRole->id);
            }

            Log::info('Created new WhatsApp guest user', [
                'user_id' => $user->id,
                'phone' => $cleanPhone,
                'name' => $userName,
            ]);

            // Send welcome message if enabled (only for new guest users)
            if (config('chatbot.whatsapp.welcome_message', true) && $user->is_whatsapp_guest === true) {
                $this->sendWelcomeMessage($cleanPhone, $user);
            }

            return $user;

        } catch (\Exception $e) {
            Log::error('Error finding/creating WhatsApp user', [
                'error' => $e->getMessage(),
                'phone' => $phoneNumber,
            ]);
            return null;
        }
    }

    /**
     * Send welcome message to new WhatsApp user.
     */
    private function sendWelcomeMessage(string $phoneNumber, User $user): void
    {
        try {
            $welcomeMessage = "🌾 Welcome to Ije AI, {$user->name}! 🌾\n\n";
            $welcomeMessage .= "I'm Ije, your AI farming assistant from Riwe. I'm here to help you with:\n\n";
            $welcomeMessage .= "🌱 Crop management advice\n";
            $welcomeMessage .= "🌤️ Weather forecasts for your farm\n";
            $welcomeMessage .= "🛡️ Insurance guidance\n";
            $welcomeMessage .= "💰 Financial planning\n";
            $welcomeMessage .= "📊 Market insights\n\n";
            $welcomeMessage .= "💡 *Want full access to Riwe features?*\n";
            $welcomeMessage .= "Type 'SIGNUP' to create your free Riwe account and unlock:\n";
            $welcomeMessage .= "• Farm management tools\n";
            $welcomeMessage .= "• Insurance services\n";
            $welcomeMessage .= "• Financial services\n";
            $welcomeMessage .= "• Market insights\n\n";
            $welcomeMessage .= "Just send me a message and I'll help you on your farming journey! 🚀";

            $result = $this->whatsappService->sendMessage($phoneNumber, $welcomeMessage);

            if ($result['success']) {
                Log::info('Welcome message sent to new WhatsApp user', [
                    'user_id' => $user->id,
                    'phone' => $phoneNumber,
                ]);
            } else {
                Log::error('Failed to send welcome message', [
                    'user_id' => $user->id,
                    'phone' => $phoneNumber,
                    'error' => $result['message'],
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error sending welcome message', [
                'user_id' => $user->id,
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle signup command from WhatsApp user.
     */
    private function handleSignupCommand(User $user, string $phoneNumber): string
    {
        Log::info('Signup command received', [
            'user_id' => $user->id,
            'phone' => $phoneNumber
        ]);

        $signupMessage = "🚀 *Create Your Riwe Account* 🚀\n\n";
        $signupMessage .= "Great! Let's upgrade your account to unlock all Riwe features.\n\n";
        $signupMessage .= "📧 *Step 1: Provide your email*\n";
        $signupMessage .= "Reply with: `SIGNUP EMAIL: <EMAIL>`\n\n";
        $signupMessage .= "Example: `SIGNUP EMAIL: <EMAIL>`\n\n";
        $signupMessage .= "✅ Benefits of full account:\n";
        $signupMessage .= "• Save your farm data\n";
        $signupMessage .= "• Access insurance services\n";
        $signupMessage .= "• Get loans and financial services\n";
        $signupMessage .= "• Track your crops and yields\n";
        $signupMessage .= "• Receive personalized insights\n\n";
        $signupMessage .= "💡 Your phone number will be verified automatically!";

        return $signupMessage;
    }

    /**
     * Handle signup with email from WhatsApp user.
     */
    private function handleSignupWithEmail(User $user, string $email, string $phoneNumber): string
    {
        try {
            // Validate email
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return "❌ Invalid email format. Please try again with a valid email:\n\nExample: `SIGNUP EMAIL: <EMAIL>`";
            }

            // Check if email already exists
            $existingUser = User::where('email', $email)->first();
            if ($existingUser && $existingUser->id !== $user->id) {
                return "❌ This email is already registered. Please use a different email or contact support if this is your email.";
            }

            Log::info('Processing signup with email', [
                'user_id' => $user->id,
                'email' => $email,
                'phone' => $phoneNumber
            ]);

            // Update user to full account
            $user->update([
                'email' => $email,
                'is_whatsapp_guest' => false,
                'email_verified_at' => now(), // Auto-verify since they're using WhatsApp
            ]);

            // Generate a temporary password
            $tempPassword = $this->generateTempPassword();
            $user->update(['password' => bcrypt($tempPassword)]);

            Log::info('User upgraded to full account', [
                'user_id' => $user->id,
                'email' => $email,
                'phone' => $phoneNumber
            ]);

            // Send success message with login details
            $successMessage = "🎉 *Account Created Successfully!* 🎉\n\n";
            $successMessage .= "Welcome to Riwe, {$user->name}!\n\n";
            $successMessage .= "📧 *Your Login Details:*\n";
            $successMessage .= "Email: {$email}\n";
            $successMessage .= "Password: `{$tempPassword}`\n";
            $successMessage .= "Phone: {$phoneNumber}\n\n";
            $successMessage .= "🌐 *Access Your Account:*\n";
            $successMessage .= "Web: " . config('app.url') . "/login\n";
            $successMessage .= "Mobile: Download Riwe app\n\n";
            $successMessage .= "🔒 *Security Tip:* Change your password after first login\n\n";
            $successMessage .= "✅ Your account is now fully activated! You can continue chatting here or log in to the web platform for more features.";

            return $successMessage;

        } catch (\Exception $e) {
            Log::error('Signup error', [
                'user_id' => $user->id,
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return "❌ Sorry, there was an error creating your account. Please try again or contact support.";
        }
    }

    /**
     * Generate temporary password for new users.
     */
    private function generateTempPassword(): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';
        for ($i = 0; $i < 8; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }
        return $password;
    }

    /**
     * Validate WhatsApp webhook signature.
     */
    private function validateSignature(Request $request): bool
    {
        try {
            $signature = $request->header('X-Hub-Signature-256');
            $appSecret = config('services.whatsapp.app_secret');

            if (!$signature || !$appSecret) {
                return false;
            }

            $payload = $request->getContent();
            $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $appSecret);

            return hash_equals($expectedSignature, $signature);

        } catch (\Exception $e) {
            Log::error('Error validating WhatsApp signature', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Check if WhatsApp integration is enabled.
     */
    private function isWhatsAppEnabled(): bool
    {
        try {
            // Check configuration from database first
            $config = \App\Models\ChatbotConfiguration::where('key', 'whatsapp_enabled')
                ->where('group', 'whatsapp')
                ->first();

            if ($config) {
                return $config->getValue() === true;
            }

            // Fallback to config file
            return config('chatbot.whatsapp.enabled', true);

        } catch (\Exception $e) {
            Log::error('Error checking WhatsApp enabled status', ['error' => $e->getMessage()]);
            // Default to enabled if there's an error checking
            return true;
        }
    }

    /**
     * Get welcome message with quick start guide
     */
    private function getWelcomeMessage(): string
    {
        return "🌾 *Welcome to Ije AI!* 👋\n\n" .
               "I'm your intelligent farming assistant, here to help you succeed! 🚜\n\n" .
               "🎯 *Quick Start:*\n" .
               "• Type 'guide' for complete instructions\n" .
               "• Type 'examples' to see sample questions\n" .
               "• Type 'menu' to explore all services\n\n" .
               "💬 *Try asking me:*\n" .
               "\"Weather in Lagos\"\n" .
               "\"Price of maize in Nigeria\"\n" .
               "\"How to plant tomatoes\"\n\n" .
               "Ready to boost your farm productivity? 🌱";
    }

    /**
     * Get quick start guide
     */
    private function getQuickStartGuide(): string
    {
        return "💡 *Quick Tips:*\n" .
               "• Ask specific questions for better answers\n" .
               "• Include your location for weather/prices\n" .
               "• Send plant photos for disease analysis\n" .
               "• Type 'help' anytime for guidance";
    }

    /**
     * Get detailed help menu
     */
    private function getDetailedHelpMenu(): string
    {
        return "🌾 *Ije AI Complete Help Guide* 📚\n\n" .

               "🌤️ *WEATHER FORECASTS*\n" .
               "Ask: \"Weather in [your location]\"\n" .
               "Examples:\n" .
               "• \"Weather in Lagos\"\n" .
               "• \"Forecast for Nairobi\"\n" .
               "• \"Will it rain in Dublin?\"\n" .
               "• \"Temperature in Morocco\"\n\n" .

               "💰 *MARKET PRICES*\n" .
               "Ask: \"Price of [crop] in [location]\"\n" .
               "Examples:\n" .
               "• \"Price of maize in Nigeria\"\n" .
               "• \"Market price of rice today\"\n" .
               "• \"How much does tomato cost?\"\n" .
               "• \"Cowpeas price in Kenya\"\n\n" .

               "🌱 *CROP ADVISORY*\n" .
               "Ask about planting, care, or problems:\n" .
               "• \"How to plant cassava?\"\n" .
               "• \"Best time to harvest rice\"\n" .
               "• \"My maize leaves are yellow\"\n" .
               "• \"Fertilizer for tomatoes\"\n\n" .

               "📸 *PLANT ANALYSIS*\n" .
               "Send a photo of your plant with description:\n" .
               "• Take clear photo of affected plant\n" .
               "• Add caption describing the problem\n" .
               "• I'll analyze pests & diseases\n\n" .

               "Type 'examples' for more sample questions! 🎯";
    }

    /**
     * Get main menu
     */
    private function getMainMenu(): string
    {
        return "📋 *Ije AI Services Menu* 🌾\n\n" .

               "🌤️ *Weather Services*\n" .
               "• Real-time weather updates\n" .
               "• 7-day forecasts\n" .
               "• Farming weather advice\n" .
               "• Rain predictions\n\n" .

               "💰 *Market Intelligence*\n" .
               "• Current crop prices\n" .
               "• Price trends & analysis\n" .
               "• Best selling times\n" .
               "• Market opportunities\n\n" .

               "🌱 *Crop Advisory*\n" .
               "• Planting guidance\n" .
               "• Pest & disease control\n" .
               "• Fertilizer recommendations\n" .
               "• Harvest timing\n\n" .

               "📸 *Plant Health Analysis*\n" .
               "• Disease identification\n" .
               "• Pest detection\n" .
               "• Treatment recommendations\n" .
               "• Prevention tips\n\n" .

               "🛡️ *Insurance & Finance*\n" .
               "• Crop insurance options\n" .
               "• Farm loans & credit\n" .
               "• Risk management\n" .
               "• Financial planning\n\n" .

               "📊 *Farm Analytics*\n" .
               "• Yield optimization\n" .
               "• Cost analysis\n" .
               "• Profit maximization\n" .
               "• Performance tracking\n\n" .

               "💬 Just ask me anything! Type 'guide' for detailed instructions.";
    }

    /**
     * Get complete user guide
     */
    private function getCompleteUserGuide(): string
    {
        return "📖 *Complete Ije AI User Guide* 🌾\n\n" .

               "🎯 *HOW TO ASK QUESTIONS*\n\n" .

               "1️⃣ *Weather Queries*\n" .
               "Format: \"Weather in [location]\"\n" .
               "✅ Good: \"Weather in Lagos\"\n" .
               "✅ Good: \"Forecast for Nairobi\"\n" .
               "✅ Good: \"Will it rain in Morocco?\"\n" .
               "❌ Avoid: \"Weather\" (too vague)\n\n" .

               "2️⃣ *Market Price Queries*\n" .
               "Format: \"Price of [crop] in [location]\"\n" .
               "✅ Good: \"Price of maize in Nigeria\"\n" .
               "✅ Good: \"How much does rice cost?\"\n" .
               "✅ Good: \"Tomato market price today\"\n" .
               "❌ Avoid: \"Prices\" (too general)\n\n" .

               "3️⃣ *Crop Advisory*\n" .
               "Be specific about your crop and issue:\n" .
               "✅ Good: \"How to plant cassava in rainy season?\"\n" .
               "✅ Good: \"My tomato leaves have brown spots\"\n" .
               "✅ Good: \"Best fertilizer for maize\"\n" .
               "❌ Avoid: \"Help with farming\" (too broad)\n\n" .

               "4️⃣ *Plant Photos*\n" .
               "• Take clear, well-lit photos\n" .
               "• Show affected parts clearly\n" .
               "• Add description of the problem\n" .
               "• Include crop type if known\n\n" .

               "💡 *PRO TIPS*\n" .
               "• Include your location for better advice\n" .
               "• Ask follow-up questions\n" .
               "• Be specific about your farming goals\n" .
               "• Mention your experience level\n\n" .

               "Type 'examples' to see more sample questions! 🚀";
    }

    /**
     * Get capabilities overview
     */
    private function getCapabilitiesOverview(): string
    {
        return "🚀 *What Ije AI Can Do For You* 🌾\n\n" .

               "🌍 *GLOBAL COVERAGE*\n" .
               "• Weather for ANY location worldwide\n" .
               "• Market prices for crops globally\n" .
               "• Farming advice for all climates\n" .
               "• 24/7 availability in multiple languages\n\n" .

               "🎯 *PRECISION AGRICULTURE*\n" .
               "• Exact weather forecasts with farming advice\n" .
               "• Real-time market prices (₦/kg, $/ton)\n" .
               "• Specific crop recommendations\n" .
               "• Personalized farming strategies\n\n" .

               "🔬 *ADVANCED ANALYSIS*\n" .
               "• AI-powered plant disease detection\n" .
               "• Pest identification from photos\n" .
               "• Soil health recommendations\n" .
               "• Yield optimization strategies\n\n" .

               "💼 *BUSINESS INTELLIGENCE*\n" .
               "• Market trend analysis\n" .
               "• Price forecasting\n" .
               "• Profit optimization\n" .
               "• Risk management advice\n\n" .

               "🛡️ *COMPREHENSIVE SUPPORT*\n" .
               "• Insurance guidance\n" .
               "• Financial planning\n" .
               "• Government schemes info\n" .
               "• Technology recommendations\n\n" .

               "Ready to transform your farming? Ask me anything! 💪";
    }

    /**
     * Get example queries
     */
    private function getExampleQueries(): string
    {
        return "💬 *Sample Questions You Can Ask* 🌾\n\n" .

               "🌤️ *WEATHER EXAMPLES*\n" .
               "• \"Weather in Lagos\"\n" .
               "• \"Forecast for Nairobi this week\"\n" .
               "• \"Will it rain in Morocco tomorrow?\"\n" .
               "• \"Temperature in Dublin today\"\n" .
               "• \"Best weather for planting maize\"\n\n" .

               "💰 *MARKET PRICE EXAMPLES*\n" .
               "• \"Price of maize in Nigeria\"\n" .
               "• \"How much does rice cost today?\"\n" .
               "• \"Tomato market price in Kenya\"\n" .
               "• \"Cowpeas price per kg\"\n" .
               "• \"Best time to sell cassava\"\n\n" .

               "🌱 *CROP ADVISORY EXAMPLES*\n" .
               "• \"How to plant tomatoes in rainy season?\"\n" .
               "• \"My maize leaves are turning yellow\"\n" .
               "• \"Best fertilizer for rice farming\"\n" .
               "• \"When to harvest cassava?\"\n" .
               "• \"How to control pests on beans\"\n\n" .

               "🔬 *PLANT ANALYSIS EXAMPLES*\n" .
               "• Send photo + \"What's wrong with my tomato?\"\n" .
               "• Send photo + \"Identify this pest on maize\"\n" .
               "• Send photo + \"Is this plant disease serious?\"\n" .
               "• Send photo + \"Treatment for these spots\"\n\n" .

               "💼 *BUSINESS EXAMPLES*\n" .
               "• \"Crop insurance for maize farming\"\n" .
               "• \"Loan options for small farmers\"\n" .
               "• \"Most profitable crops in Nigeria\"\n" .
               "• \"How to reduce farming costs\"\n\n" .

               "🎯 *SPECIFIC LOCATION EXAMPLES*\n" .
               "• \"Best crops for Lagos climate\"\n" .
               "• \"Farming calendar for Kenya\"\n" .
               "• \"Irrigation needs in Morocco\"\n" .
               "• \"Greenhouse farming in Ireland\"\n\n" .

               "Just copy and modify these examples! 📝\n" .
               "Type 'help' for more guidance anytime.";
    }

    /**
     * Get weather guidance
     */
    private function getWeatherGuidance(): string
    {
        return "🌤️ *Weather Forecasts Available Globally!* 🌍\n\n" .
               "📍 *How to Ask:*\n" .
               "\"Weather in [your location]\"\n\n" .
               "🌟 *Examples:*\n" .
               "• \"Weather in Lagos\" 🇳🇬\n" .
               "• \"Forecast for Nairobi\" 🇰🇪\n" .
               "• \"Will it rain in Dublin?\" 🇮🇪\n" .
               "• \"Temperature in Morocco\" 🇲🇦\n" .
               "• \"Weather in New York\" 🇺🇸\n\n" .
               "🎯 *I provide:*\n" .
               "• Current temperature & conditions\n" .
               "• 24-hour detailed forecast\n" .
               "• Farming weather advice\n" .
               "• Rain probability & timing\n\n" .
               "Try it now! 🚀";
    }

    /**
     * Get weather examples
     */
    private function getWeatherExamples(): string
    {
        return "🌤️ *Weather Examples:*\n" .
               "• \"Weather in [your city]\"\n" .
               "• \"Forecast for [location]\"\n" .
               "• \"Will it rain in [area]?\"\n" .
               "• \"Temperature in [place]\"";
    }

    /**
     * Get market price guidance
     */
    private function getMarketPriceGuidance(): string
    {
        return "💰 *Real-Time Market Prices!* 📊\n\n" .
               "📍 *How to Ask:*\n" .
               "\"Price of [crop] in [location]\"\n\n" .
               "🌟 *Examples:*\n" .
               "• \"Price of maize in Nigeria\" 🌽\n" .
               "• \"How much does rice cost?\" 🌾\n" .
               "• \"Tomato market price today\" 🍅\n" .
               "• \"Cowpeas price in Kenya\" 🫘\n" .
               "• \"Cassava price per kg\" 🥔\n\n" .
               "🎯 *I provide:*\n" .
               "• Current price per kg (₦/kg)\n" .
               "• Wholesale vs retail prices\n" .
               "• Price trends (up/down)\n" .
               "• Best selling times\n\n" .
               "💡 *Supported crops:* Maize, Rice, Tomato, Cassava, Beans, Yam, Plantain, and 50+ more!\n\n" .
               "Ask now for instant prices! 🚀";
    }

    /**
     * Get crop guidance
     */
    private function getCropGuidance(): string
    {
        return "🌱 *Complete Crop Advisory!* 🚜\n\n" .
               "📍 *How to Ask:*\n" .
               "Be specific about your crop and question\n\n" .
               "🌟 *Examples:*\n" .
               "• \"How to plant cassava?\" 🥔\n" .
               "• \"Best time to harvest rice\" 🌾\n" .
               "• \"My maize leaves are yellow\" 🌽\n" .
               "• \"Fertilizer for tomatoes\" 🍅\n" .
               "• \"Pest control for beans\" 🫘\n\n" .
               "🎯 *I help with:*\n" .
               "• Planting & seeding guidance\n" .
               "• Fertilizer recommendations\n" .
               "• Pest & disease control\n" .
               "• Harvest timing\n" .
               "• Yield optimization\n\n" .
               "💡 *Pro tip:* Include your location for climate-specific advice!\n\n" .
               "What crop question do you have? 🌾";
    }

    /**
     * Get beginner guidance
     */
    private function getBeginnerGuidance(): string
    {
        return "🌱 *Welcome New Farmer!* 👨‍🌾\n\n" .
               "🎯 *Perfect! I'll guide you step by step.*\n\n" .
               "📚 *Start with these:*\n" .
               "• \"Best crops for beginners\"\n" .
               "• \"How to prepare soil for farming\"\n" .
               "• \"Farming calendar for [your location]\"\n" .
               "• \"Basic farming tools needed\"\n\n" .
               "🌟 *Popular beginner crops:*\n" .
               "• Tomatoes 🍅 (fast growing)\n" .
               "• Beans 🫘 (easy to grow)\n" .
               "• Lettuce 🥬 (quick harvest)\n" .
               "• Maize 🌽 (good income)\n\n" .
               "💡 *My beginner-friendly features:*\n" .
               "• Step-by-step planting guides\n" .
               "• Simple farming calendars\n" .
               "• Cost-effective methods\n" .
               "• Common mistake prevention\n\n" .
               "What would you like to start with? 🚀";
    }

    /**
     * Get photo guidance
     */
    private function getPhotoGuidance(): string
    {
        return "📸 *Plant Health Analysis!* 🔬\n\n" .
               "🎯 *How to get the best analysis:*\n\n" .
               "📷 *Photo Tips:*\n" .
               "• Take clear, well-lit photos\n" .
               "• Show affected parts up close\n" .
               "• Include whole plant if possible\n" .
               "• Multiple angles help\n\n" .
               "💬 *Add a description:*\n" .
               "• What crop is it?\n" .
               "• What's the problem?\n" .
               "• When did it start?\n" .
               "• Your location (for climate context)\n\n" .
               "🌟 *Example messages:*\n" .
               "📸 + \"What's wrong with my tomato leaves?\"\n" .
               "📸 + \"Identify this pest on maize\"\n" .
               "📸 + \"Are these spots on beans serious?\"\n\n" .
               "🎯 *I'll identify:*\n" .
               "• Plant diseases\n" .
               "• Pest problems\n" .
               "• Nutrient deficiencies\n" .
               "• Treatment recommendations\n\n" .
               "Send your plant photo now! 📱";
    }
}
